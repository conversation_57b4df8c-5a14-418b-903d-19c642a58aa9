#!/usr/bin/env python3
"""
亚像素精度验证测试
验证相位相关算法的亚像素精度，确保位移检测精度达到0.1像素以内

测试方法：
1. 创建已知位移的合成图像对
2. 使用相位相关算法检测位移
3. 比较检测结果与真实位移
4. 验证精度是否达到0.1像素以内
"""

import os
import sys
import numpy as np
import cv2
import importlib.util
from typing import List, Tuple
import matplotlib.pyplot as plt

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        sys.exit(1)

def create_test_image(size: Tuple[int, int] = (512, 512)) -> np.ndarray:
    """创建测试图像"""
    h, w = size
    img = np.zeros((h, w), dtype=np.uint8)
    
    # 添加各种特征
    # 1. 随机噪声背景
    noise = np.random.randint(0, 50, (h, w), dtype=np.uint8)
    img += noise
    
    # 2. 几何图案
    center_y, center_x = h // 2, w // 2
    
    # 圆形
    y, x = np.ogrid[:h, :w]
    circle_mask = (y - center_y)**2 + (x - center_x)**2 < (min(h, w) // 6)**2
    img[circle_mask] = 200
    
    # 矩形
    rect_y1, rect_y2 = center_y - h//8, center_y + h//8
    rect_x1, rect_x2 = center_x - w//8, center_x + w//8
    img[rect_y1:rect_y2, rect_x1:rect_x2] = 150
    
    # 3. 线条特征
    # 水平线
    img[center_y-2:center_y+2, :] = 255
    # 垂直线
    img[:, center_x-2:center_x+2] = 255
    
    # 4. 对角线
    for i in range(min(h, w)):
        if 0 <= i < h and 0 <= i < w:
            img[i, i] = 180
        if 0 <= i < h and 0 <= w-1-i < w:
            img[i, w-1-i] = 180
    
    # 5. 高频纹理
    for i in range(0, h, 20):
        for j in range(0, w, 20):
            if i < h and j < w:
                img[i:min(i+10, h), j:min(j+10, w)] = 120
    
    return img

def apply_subpixel_shift(img: np.ndarray, shift_y: float, shift_x: float) -> np.ndarray:
    """应用亚像素位移"""
    h, w = img.shape
    
    # 使用双线性插值实现亚像素位移
    # 创建变换矩阵
    M = np.float32([[1, 0, shift_x], [0, 1, shift_y]])
    
    # 应用仿射变换
    shifted_img = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
    
    return shifted_img

def test_subpixel_accuracy_single(stitcher, true_shift: Tuple[float, float], 
                                 image_size: Tuple[int, int] = (512, 512)) -> Tuple[float, float, float]:
    """测试单个位移的亚像素精度"""
    true_shift_y, true_shift_x = true_shift
    
    # 创建参考图像
    img1 = create_test_image(image_size)
    
    # 创建位移图像
    img2 = apply_subpixel_shift(img1, true_shift_y, true_shift_x)
    
    # 使用相位相关算法检测位移
    pos1 = np.array([0.0, 0.0])
    pos2 = np.array([0.0, 0.0])  # 假设没有预知位移
    
    detected_shift, correlation = stitcher.phase_correlation_registration(
        img1, img2, pos1, pos2, num_peaks=5, subpixel_accuracy=True
    )
    
    # 计算误差
    error_y = abs(detected_shift[0] - true_shift_y)
    error_x = abs(detected_shift[1] - true_shift_x)
    total_error = np.sqrt(error_y**2 + error_x**2)
    
    return error_y, error_x, total_error

def test_subpixel_accuracy_comprehensive():
    """全面的亚像素精度测试"""
    print("=" * 60)
    print("相位相关算法亚像素精度验证测试")
    print("=" * 60)
    
    # 加载拼接模块
    stitching = load_stitching_module()
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    # 创建拼接器
    config = StitchingConfig()
    stitcher = ImageStitcher(config)
    
    # 测试用例：不同的亚像素位移
    test_shifts = [
        (0.1, 0.0),   # 纯Y方向小位移
        (0.0, 0.1),   # 纯X方向小位移
        (0.1, 0.1),   # 对角小位移
        (0.3, 0.2),   # 中等位移
        (0.5, 0.4),   # 较大位移
        (-0.2, 0.3),  # 负位移
        (0.7, -0.1),  # 混合位移
        (0.25, 0.75), # 四分之一像素位移
        (0.33, 0.67), # 三分之一像素位移
        (0.9, 0.8),   # 接近整像素位移
    ]
    
    print(f"测试 {len(test_shifts)} 个不同的亚像素位移...")
    print()
    
    results = []
    passed_tests = 0
    target_accuracy = 0.1  # 目标精度：0.1像素
    
    for i, (shift_y, shift_x) in enumerate(test_shifts):
        print(f"测试 {i+1:2d}: 真实位移 ({shift_y:+.2f}, {shift_x:+.2f})", end=" ")
        
        try:
            error_y, error_x, total_error = test_subpixel_accuracy_single(
                stitcher, (shift_y, shift_x), image_size=(512, 512)
            )
            
            results.append({
                'true_shift': (shift_y, shift_x),
                'error_y': error_y,
                'error_x': error_x,
                'total_error': total_error,
                'passed': total_error <= target_accuracy
            })
            
            status = "✅ PASS" if total_error <= target_accuracy else "❌ FAIL"
            print(f"-> 误差: {total_error:.3f} 像素 {status}")
            
            if total_error <= target_accuracy:
                passed_tests += 1
                
        except Exception as e:
            print(f"-> ❌ ERROR: {e}")
            results.append({
                'true_shift': (shift_y, shift_x),
                'error_y': float('inf'),
                'error_x': float('inf'),
                'total_error': float('inf'),
                'passed': False
            })
    
    print()
    print("=" * 60)
    print("测试结果统计")
    print("=" * 60)
    
    # 统计结果
    valid_results = [r for r in results if r['total_error'] != float('inf')]
    
    if valid_results:
        errors = [r['total_error'] for r in valid_results]
        mean_error = np.mean(errors)
        max_error = np.max(errors)
        min_error = np.min(errors)
        std_error = np.std(errors)
        
        print(f"有效测试数量: {len(valid_results)}/{len(test_shifts)}")
        print(f"通过测试数量: {passed_tests}/{len(test_shifts)} ({passed_tests/len(test_shifts)*100:.1f}%)")
        print(f"目标精度: {target_accuracy:.1f} 像素")
        print()
        print(f"误差统计:")
        print(f"  平均误差: {mean_error:.4f} 像素")
        print(f"  最大误差: {max_error:.4f} 像素")
        print(f"  最小误差: {min_error:.4f} 像素")
        print(f"  标准差:   {std_error:.4f} 像素")
        print()
        
        # 详细结果
        print("详细结果:")
        print("序号  真实位移        检测误差      状态")
        print("-" * 50)
        for i, result in enumerate(results):
            if result['total_error'] != float('inf'):
                true_y, true_x = result['true_shift']
                error = result['total_error']
                status = "PASS" if result['passed'] else "FAIL"
                print(f"{i+1:2d}   ({true_y:+.2f}, {true_x:+.2f})   {error:.4f}      {status}")
        
        # 评估结果
        print()
        print("=" * 60)
        if passed_tests == len(test_shifts):
            print("🎉 所有测试通过！亚像素精度达到要求")
            print(f"✅ 算法精度: {mean_error:.4f} ± {std_error:.4f} 像素")
            print(f"✅ 目标精度: {target_accuracy:.1f} 像素")
            return True
        elif passed_tests >= len(test_shifts) * 0.8:
            print(f"⚠️  大部分测试通过 ({passed_tests}/{len(test_shifts)})")
            print(f"📊 算法精度: {mean_error:.4f} ± {std_error:.4f} 像素")
            print("💡 建议进一步优化算法参数")
            return True
        else:
            print(f"❌ 测试失败 ({passed_tests}/{len(test_shifts)} 通过)")
            print(f"📊 当前精度: {mean_error:.4f} ± {std_error:.4f} 像素")
            print(f"🎯 目标精度: {target_accuracy:.1f} 像素")
            print("🔧 需要改进算法实现")
            return False
    else:
        print("❌ 所有测试都失败了")
        return False

def main():
    """主函数"""
    print("ImageJ 相位相关算法亚像素精度验证")
    print("目标：验证位移检测精度达到0.1像素以内")
    print()
    
    success = test_subpixel_accuracy_comprehensive()
    
    if success:
        print("\n🎯 亚像素精度验证成功！")
        print("算法满足高精度图像拼接的要求")
        return 0
    else:
        print("\n❌ 亚像素精度验证失败")
        print("需要进一步优化算法实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())
