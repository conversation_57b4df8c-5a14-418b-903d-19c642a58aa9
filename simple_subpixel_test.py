#!/usr/bin/env python3
"""
简化的亚像素精度测试
快速验证相位相关算法的基本功能
"""

import os
import sys
import numpy as np
import cv2
import importlib.util

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        return None

def create_simple_test_image():
    """创建简单的测试图像"""
    img = np.zeros((256, 256), dtype=np.uint8)
    
    # 添加一些特征
    cv2.circle(img, (128, 128), 50, 200, -1)
    cv2.rectangle(img, (100, 100), (156, 156), 150, 2)
    cv2.line(img, (0, 128), (255, 128), 255, 2)
    cv2.line(img, (128, 0), (128, 255), 255, 2)
    
    return img

def test_basic_phase_correlation():
    """测试基本相位相关功能"""
    print("=" * 50)
    print("简化亚像素精度测试")
    print("=" * 50)
    
    # 加载模块
    stitching = load_stitching_module()
    if stitching is None:
        return False
    
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    print("✅ 成功加载拼接模块")
    
    # 创建拼接器
    config = StitchingConfig()
    stitcher = ImageStitcher(config)
    print("✅ 成功创建拼接器")
    
    # 创建测试图像
    img1 = create_simple_test_image()
    print("✅ 创建测试图像")
    
    # 测试几个简单的位移
    test_cases = [
        (0.0, 0.0),   # 无位移
        (1.0, 0.0),   # 整像素位移
        (0.5, 0.0),   # 半像素位移
        (0.0, 1.0),   # Y方向位移
        (1.0, 1.0),   # 对角位移
    ]
    
    print(f"\n测试 {len(test_cases)} 个位移案例:")
    
    passed = 0
    for i, (dy, dx) in enumerate(test_cases):
        print(f"  测试 {i+1}: 位移 ({dy:.1f}, {dx:.1f})", end=" ")
        
        try:
            # 创建位移图像
            M = np.float32([[1, 0, dx], [0, 1, dy]])
            img2 = cv2.warpAffine(img1, M, (256, 256))
            
            # 检测位移
            pos1 = np.array([0.0, 0.0])
            pos2 = np.array([0.0, 0.0])
            
            detected_shift, correlation = stitcher.phase_correlation_registration(
                img1, img2, pos1, pos2, num_peaks=3, subpixel_accuracy=True
            )
            
            # 计算误差
            error_y = abs(detected_shift[0] - dy)
            error_x = abs(detected_shift[1] - dx)
            total_error = np.sqrt(error_y**2 + error_x**2)
            
            if total_error < 0.5:  # 宽松的精度要求
                print(f"✅ 误差: {total_error:.3f}")
                passed += 1
            else:
                print(f"❌ 误差: {total_error:.3f}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print(f"\n结果: {passed}/{len(test_cases)} 测试通过")
    
    if passed >= len(test_cases) * 0.8:
        print("🎉 基本功能测试通过！")
        return True
    else:
        print("❌ 基本功能测试失败")
        return False

def main():
    """主函数"""
    print("ImageJ 相位相关算法 - 简化亚像素精度测试")
    print()
    
    success = test_basic_phase_correlation()
    
    if success:
        print("\n✅ 测试完成！相位相关算法基本功能正常")
        print("📝 算法特点:")
        print("   • 基于FFT的相位相关算法")
        print("   • 支持亚像素精度定位")
        print("   • 多种插值方法组合")
        print("   • 鲁棒性增强处理")
        return 0
    else:
        print("\n❌ 测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
