#!/usr/bin/env python3
"""
直接相位相关测试
绕过ROI提取，直接测试相位相关核心算法
"""

import numpy as np
import cv2
from scipy.fft import fft2, ifft2, fftshift
import importlib.util

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        return None

def direct_phase_correlation_test():
    """直接测试相位相关算法"""
    print("=" * 60)
    print("直接相位相关测试")
    print("=" * 60)
    
    # 加载模块
    stitching = load_stitching_module()
    if stitching is None:
        return False
    
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    # 创建拼接器
    config = StitchingConfig()
    stitcher = ImageStitcher(config)
    
    # 创建测试图像
    img1 = np.zeros((128, 128), dtype=np.uint8)
    cv2.circle(img1, (64, 64), 20, 255, -1)
    cv2.rectangle(img1, (50, 50), (78, 78), 128, 2)
    
    # 测试不同位移
    test_shifts = [(0, 0), (1, 0), (0, 1), (2, 3), (-1, 2)]
    
    for shift_y, shift_x in test_shifts:
        print(f"\n测试位移: ({shift_y}, {shift_x})")
        
        # 创建位移图像
        M = np.float32([[1, 0, shift_x], [0, 1, shift_y]])
        img2 = cv2.warpAffine(img1, M, (128, 128))
        
        # 直接调用核心算法
        try:
            # 转换为float64
            roi1_f = img1.astype(np.float64) / 255.0
            roi2_f = img2.astype(np.float64) / 255.0
            
            # 应用汉宁窗
            roi1_f = stitcher._apply_hanning_window(roi1_f)
            roi2_f = stitcher._apply_hanning_window(roi2_f)
            
            # 零填充
            roi1_padded, roi2_padded = stitcher._zero_pad_to_fft_size(roi1_f, roi2_f)
            
            print(f"  填充后尺寸: {roi1_padded.shape}")
            
            # 计算相位相关
            detected_shift, correlation = stitcher._compute_phase_correlation(
                roi1_padded, roi2_padded, num_peaks=3, subpixel_accuracy=False
            )
            
            print(f"  检测位移: ({detected_shift[0]:.3f}, {detected_shift[1]:.3f})")
            print(f"  相关系数: {correlation:.6f}")
            
            error = np.linalg.norm(detected_shift - np.array([shift_y, shift_x]))
            print(f"  误差: {error:.3f}")
            
            # 测试亚像素版本
            detected_shift_sub, correlation_sub = stitcher._compute_phase_correlation(
                roi1_padded, roi2_padded, num_peaks=3, subpixel_accuracy=True
            )
            
            print(f"  亚像素位移: ({detected_shift_sub[0]:.3f}, {detected_shift_sub[1]:.3f})")
            print(f"  亚像素相关: {correlation_sub:.6f}")
            
            error_sub = np.linalg.norm(detected_shift_sub - np.array([shift_y, shift_x]))
            print(f"  亚像素误差: {error_sub:.3f}")
            
        except Exception as e:
            print(f"  ❌ 错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("直接相位相关算法测试")
    print("绕过ROI提取，直接测试核心算法")
    print()
    
    direct_phase_correlation_test()
    
    return 0

if __name__ == "__main__":
    main()
