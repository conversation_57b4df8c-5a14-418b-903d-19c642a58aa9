#!/usr/bin/env python3
"""
调试纯相位相关算法
"""

import numpy as np
import cv2
from scipy.fft import fft2, ifft2, fftshift
import importlib.util

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        return None

def test_phase_correlation_core():
    """测试相位相关核心算法"""
    print("=" * 60)
    print("调试纯相位相关算法")
    print("=" * 60)
    
    # 加载模块
    stitching = load_stitching_module()
    if stitching is None:
        return False
    
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    # 创建拼接器
    config = StitchingConfig()
    stitcher = ImageStitcher(config)
    
    # 加载两张真实的相邻图像进行测试
    try:
        img1_path = "Image_SX88/s_0001.jpg"
        img2_path = "Image_SX88/s_0002.jpg"  # 水平相邻
        
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)
        
        if img1 is None or img2 is None:
            print("❌ 无法加载测试图像")
            return False
        
        print(f"✅ 成功加载图像")
        print(f"   图像1尺寸: {img1.shape}")
        print(f"   图像2尺寸: {img2.shape}")
        
        # 设置位置（基于TileConfiguration.txt）
        pos1 = np.array([0.0, 0.0])
        pos2 = np.array([0.0, 2203.2])  # 水平相邻
        
        print(f"   位置1: {pos1}")
        print(f"   位置2: {pos2}")
        print()
        
        # 测试重叠区域提取
        print("1. 测试重叠区域提取:")
        try:
            dx = pos2[1] - pos1[1]
            dy = pos2[0] - pos1[0]
            print(f"   位移: dx={dx}, dy={dy}")
            
            if abs(dx) > abs(dy):
                roi1, roi2 = stitcher._extract_horizontal_overlap_regions(img1, img2, dx > 0)
                print(f"   水平重叠区域: ROI1={roi1.shape}, ROI2={roi2.shape}")
            else:
                roi1, roi2 = stitcher._extract_vertical_overlap_regions(img1, img2, dy > 0)
                print(f"   垂直重叠区域: ROI1={roi1.shape}, ROI2={roi2.shape}")
            
        except Exception as e:
            print(f"   ❌ 重叠区域提取失败: {e}")
            return False
        
        print()
        
        # 测试相位相关核心算法
        print("2. 测试相位相关核心算法:")
        try:
            shift, correlation = stitcher._apply_phase_correlation(roi1, roi2)
            print(f"   检测位移: [{shift[0]:.3f}, {shift[1]:.3f}]")
            print(f"   相关系数: {correlation:.6f}")
            
            # 预期位移（基于位置）
            expected_shift = pos2 - pos1
            print(f"   预期位移: [{expected_shift[0]:.3f}, {expected_shift[1]:.3f}]")
            
            error = np.linalg.norm(shift - expected_shift)
            print(f"   误差: {error:.3f}")
            
        except Exception as e:
            print(f"   ❌ 相位相关算法失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print()
        
        # 测试完整的纯相位相关配准
        print("3. 测试完整的纯相位相关配准:")
        try:
            final_shift, final_correlation = stitcher.pure_phase_correlation_registration(img1, img2, pos1, pos2)
            print(f"   最终位移: [{final_shift[0]:.3f}, {final_shift[1]:.3f}]")
            print(f"   最终相关: {final_correlation:.6f}")
            
        except Exception as e:
            print(f"   ❌ 完整配准失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print()
        print("✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("纯相位相关算法调试工具")
    print()
    
    success = test_phase_correlation_core()
    
    if success:
        print("\n🎉 调试完成！相位相关算法工作正常")
    else:
        print("\n❌ 调试失败，需要修复算法")
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
