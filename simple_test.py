#!/usr/bin/env python3
"""
简单测试脚本 - 验证相位相关拼接算法的核心功能
"""

import os
import sys
import numpy as np
import cv2

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        
        ImageStitcher = stitching_module.ImageStitcher
        StitchingConfig = stitching_module.StitchingConfig
        print("✅ 成功导入拼接模块")
        return ImageStitcher, StitchingConfig
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return None, None

def test_config_parsing():
    """测试配置文件解析"""
    print("\n测试配置文件解析...")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        
        ConfigurationParser = stitching_module.ConfigurationParser
        Logger = stitching_module.Logger
        
        logger = Logger()
        config_path = "image_55/TileConfiguration.txt"
        
        if os.path.exists(config_path):
            filenames, positions = ConfigurationParser.parse_tile_config(config_path, logger)
            print(f"✅ 成功解析配置文件")
            print(f"   图像数量: {len(filenames)}")
            print(f"   位置数组形状: {positions.shape}")
            print(f"   前3个文件: {filenames[:3]}")
            print(f"   前3个位置: {positions[:3]}")
            return True
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            return False
    except Exception as e:
        print(f"❌ 配置解析失败: {e}")
        return False

def test_image_loading():
    """测试图像加载"""
    print("\n测试图像加载...")
    try:
        test_image = "image_55/s_0001.jpg"
        if os.path.exists(test_image):
            img = cv2.imread(test_image)
            if img is not None:
                print(f"✅ 成功加载测试图像")
                print(f"   图像尺寸: {img.shape}")
                return True
            else:
                print(f"❌ 无法读取图像: {test_image}")
                return False
        else:
            print(f"❌ 测试图像不存在: {test_image}")
            return False
    except Exception as e:
        print(f"❌ 图像加载失败: {e}")
        return False

def test_phase_correlation():
    """测试相位相关算法核心功能"""
    print("\n测试相位相关算法...")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        
        ImageStitcher = stitching_module.ImageStitcher
        StitchingConfig = stitching_module.StitchingConfig
        
        # 创建拼接器
        config = StitchingConfig()
        stitcher = ImageStitcher(config)
        
        # 加载两张测试图像
        img1_path = "image_55/s_0001.jpg"
        img2_path = "image_55/s_0002.jpg"
        
        if os.path.exists(img1_path) and os.path.exists(img2_path):
            img1 = cv2.imread(img1_path, cv2.IMREAD_GRAYSCALE)
            img2 = cv2.imread(img2_path, cv2.IMREAD_GRAYSCALE)
            
            if img1 is not None and img2 is not None:
                # 测试相位相关配准
                pos1 = np.array([0.0, 0.0])
                pos2 = np.array([0.0, 2203.2])  # 基于TileConfiguration.txt的间距
                
                shift, correlation = stitcher.phase_correlation_registration(
                    img1, img2, pos1, pos2, num_peaks=5, subpixel_accuracy=True
                )
                
                print(f"✅ 相位相关配准成功")
                print(f"   计算位移: [{shift[0]:.2f}, {shift[1]:.2f}]")
                print(f"   相关系数: {correlation:.4f}")
                return True
            else:
                print("❌ 无法加载测试图像")
                return False
        else:
            print("❌ 测试图像文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 相位相关测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("ImageJ 相位相关拼接算法 - 简单测试")
    print("=" * 50)
    
    # 测试序列
    tests = [
        ("导入测试", test_imports),
        ("配置解析测试", test_config_parsing),
        ("图像加载测试", test_image_loading),
        ("相位相关测试", test_phase_correlation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "导入测试":
                result = test_func()
                results.append(result[0] is not None)
            else:
                result = test_func()
                results.append(result)
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过! 相位相关拼接算法准备就绪")
        return 0
    else:
        print(f"\n⚠️  {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
