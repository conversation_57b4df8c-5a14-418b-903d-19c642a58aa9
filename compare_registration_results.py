#!/usr/bin/env python3
"""
对比Fiji Stitching和Python相位相关算法的注册结果
"""

import numpy as np
import matplotlib.pyplot as plt
import re
from typing import List, Tuple, Dict

def parse_tile_config(filename: str) -> Dict[str, Tuple[float, float]]:
    """解析TileConfiguration文件"""
    positions = {}
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line.startswith('#') or line.startswith('dim') or not line:
                continue
            
            # 解析格式: filename; ; (x, y)
            match = re.match(r'([^;]+);\s*;\s*\(([^,]+),\s*([^)]+)\)', line)
            if match:
                filename = match.group(1).strip()
                x = float(match.group(2))
                y = float(match.group(3))
                positions[filename] = (x, y)
    
    return positions

def calculate_statistics(fiji_positions: Dict, python_positions: Dict) -> Dict:
    """计算统计信息"""
    stats = {
        'position_differences': [],
        'x_differences': [],
        'y_differences': [],
        'filenames': []
    }
    
    for filename in fiji_positions:
        if filename in python_positions:
            fiji_x, fiji_y = fiji_positions[filename]
            python_x, python_y = python_positions[filename]
            
            diff_x = python_x - fiji_x
            diff_y = python_y - fiji_y
            diff_magnitude = np.sqrt(diff_x**2 + diff_y**2)
            
            stats['position_differences'].append(diff_magnitude)
            stats['x_differences'].append(diff_x)
            stats['y_differences'].append(diff_y)
            stats['filenames'].append(filename)
    
    return stats

def analyze_registration_comparison():
    """分析注册结果对比"""
    print("=" * 80)
    print("Fiji Stitching vs Python相位相关算法 - 注册结果对比分析")
    print("=" * 80)
    
    # 解析两个注册文件
    try:
        fiji_file = "Image_SX88/TileConfiguration.registered.txt"
        python_file = "TileConfiguration.registered_python.txt"
        
        print(f"📁 Fiji注册文件: {fiji_file}")
        print(f"📁 Python注册文件: {python_file}")
        print()
        
        fiji_positions = parse_tile_config(fiji_file)
        python_positions = parse_tile_config(python_file)
        
        print(f"✅ Fiji注册结果: {len(fiji_positions)} 张图像")
        print(f"✅ Python注册结果: {len(python_positions)} 张图像")
        print()
        
    except Exception as e:
        print(f"❌ 文件解析失败: {e}")
        return False
    
    # 计算统计信息
    stats = calculate_statistics(fiji_positions, python_positions)
    
    if not stats['position_differences']:
        print("❌ 没有找到匹配的图像")
        return False
    
    # 统计分析
    position_diffs = np.array(stats['position_differences'])
    x_diffs = np.array(stats['x_differences'])
    y_diffs = np.array(stats['y_differences'])
    
    print("📊 位置差异统计:")
    print(f"   总体位置差异:")
    print(f"     平均差异: {np.mean(position_diffs):.2f} 像素")
    print(f"     最大差异: {np.max(position_diffs):.2f} 像素")
    print(f"     最小差异: {np.min(position_diffs):.2f} 像素")
    print(f"     标准差:   {np.std(position_diffs):.2f} 像素")
    print()
    
    print(f"   X方向差异:")
    print(f"     平均差异: {np.mean(x_diffs):.2f} 像素")
    print(f"     最大差异: {np.max(x_diffs):.2f} 像素")
    print(f"     最小差异: {np.min(x_diffs):.2f} 像素")
    print(f"     标准差:   {np.std(x_diffs):.2f} 像素")
    print()
    
    print(f"   Y方向差异:")
    print(f"     平均差异: {np.mean(y_diffs):.2f} 像素")
    print(f"     最大差异: {np.max(y_diffs):.2f} 像素")
    print(f"     最小差异: {np.min(y_diffs):.2f} 像素")
    print(f"     标准差:   {np.std(y_diffs):.2f} 像素")
    print()
    
    # 找出差异最大的图像
    max_diff_idx = np.argmax(position_diffs)
    max_diff_filename = stats['filenames'][max_diff_idx]
    max_diff_value = position_diffs[max_diff_idx]
    
    print(f"🔍 最大差异图像:")
    print(f"   文件名: {max_diff_filename}")
    print(f"   差异: {max_diff_value:.2f} 像素")
    print(f"   Fiji位置: {fiji_positions[max_diff_filename]}")
    print(f"   Python位置: {python_positions[max_diff_filename]}")
    print()
    
    # 精度评估
    print("🎯 精度评估:")
    
    # 计算精度等级
    excellent_count = np.sum(position_diffs < 10)
    good_count = np.sum((position_diffs >= 10) & (position_diffs < 50))
    fair_count = np.sum((position_diffs >= 50) & (position_diffs < 100))
    poor_count = np.sum(position_diffs >= 100)
    
    total_count = len(position_diffs)
    
    print(f"   优秀 (<10像素):   {excellent_count:3d} ({excellent_count/total_count*100:.1f}%)")
    print(f"   良好 (10-50像素): {good_count:3d} ({good_count/total_count*100:.1f}%)")
    print(f"   一般 (50-100像素):{fair_count:3d} ({fair_count/total_count*100:.1f}%)")
    print(f"   较差 (>100像素):  {poor_count:3d} ({poor_count/total_count*100:.1f}%)")
    print()
    
    # 整体评估
    avg_diff = np.mean(position_diffs)
    if avg_diff < 20:
        grade = "优秀"
        emoji = "🏆"
    elif avg_diff < 50:
        grade = "良好"
        emoji = "✅"
    elif avg_diff < 100:
        grade = "一般"
        emoji = "⚠️"
    else:
        grade = "需要改进"
        emoji = "❌"
    
    print(f"{emoji} 整体评估: {grade}")
    print(f"   平均位置差异: {avg_diff:.2f} 像素")
    
    # 算法特点分析
    print()
    print("🔬 算法特点分析:")
    
    # 分析X和Y方向的系统性偏差
    x_bias = np.mean(x_diffs)
    y_bias = np.mean(y_diffs)
    
    print(f"   系统性偏差:")
    print(f"     X方向偏差: {x_bias:.2f} 像素")
    print(f"     Y方向偏差: {y_bias:.2f} 像素")
    
    if abs(x_bias) > 20 or abs(y_bias) > 20:
        print(f"   ⚠️  检测到系统性偏差，可能需要校准")
    else:
        print(f"   ✅ 系统性偏差较小，算法表现良好")
    
    # 分析一致性
    x_consistency = np.std(x_diffs)
    y_consistency = np.std(y_diffs)
    
    print(f"   一致性分析:")
    print(f"     X方向一致性: {x_consistency:.2f} 像素 (标准差)")
    print(f"     Y方向一致性: {y_consistency:.2f} 像素 (标准差)")
    
    if x_consistency < 50 and y_consistency < 50:
        print(f"   ✅ 算法一致性良好")
    else:
        print(f"   ⚠️  算法一致性有待改进")
    
    print()
    print("=" * 80)
    print("📝 总结:")
    print(f"   Python相位相关算法与Fiji Stitching的平均差异为 {avg_diff:.2f} 像素")
    print(f"   算法整体表现: {grade}")
    
    if avg_diff < 50:
        print(f"   🎉 相位相关算法实现成功，精度满足实用要求！")
    else:
        print(f"   🔧 相位相关算法需要进一步优化以提高精度")
    
    return True

def main():
    """主函数"""
    print("TileConfiguration注册结果对比分析工具")
    print("对比Fiji Stitching和Python相位相关算法的校正效果")
    print()
    
    success = analyze_registration_comparison()
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
