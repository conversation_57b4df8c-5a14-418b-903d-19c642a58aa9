"""
高性能图像拼接系统
优化的多线程图像拼接算法，支持大规模图像拼接
支持PyTorch GPU加速融合

重构版本：
- 清晰的模块化结构
- 解决logger初始化问题  
- 改进的类型注解
- 更好的错误处理
"""

import os
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from enum import Enum
from typing import List, Tuple, Optional, Dict, Any, Union, Protocol

# 解决OpenMP库冲突问题 - 在导入其他库之前设置
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
import re
import time
import logging
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from skimage.registration import phase_cross_correlation
from scipy.optimize import least_squares
from scipy import ndimage
from scipy.ndimage import maximum_filter
from tqdm import tqdm
from dataclasses import dataclass, field

# PyTorch GPU支持
PYTORCH_AVAILABLE = False
torch = None
try:
    import torch
    import torch.nn.functional as F
    PYTORCH_AVAILABLE = True
    print(f"PyTorch可用: {torch.__version__}")
    if torch.cuda.is_available():
        print(f"CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"CUDA内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("CUDA不可用，将使用CPU模式")
except ImportError:
    print("PyTorch不可用，将使用传统CPU融合")


class FeatureDetectorType(Enum):
    """特征检测器类型枚举"""
    SIFT = "SIFT"
    ORB = "ORB"
    AKAZE = "AKAZE"


class SaveFormat(Enum):
    """保存格式枚举"""
    JPEG = "jpg"
    PNG = "png"
    TIFF = "tiff"


@dataclass
class StitchingConfig:
    """拼接算法配置参数"""
    # 特征匹配参数
    feature_count: int = 800
    overlap_ratio: float = 0.08
    min_confidence: float = 0.1
    fallback_confidence: float = 0.05
    
    # 全局优化参数
    max_iterations: int = 20
    abs_threshold: float = 10.0
    ratio_threshold: float = 2.0
    max_position_change: float = 30.0
    constraint_weight: float = 0.02
    
    # 融合参数
    feather_pixels_ratio: float = 0.1
    max_feather_pixels: int = 50
    min_feather_pixels: int = 5
    memory_limit_gb: float = 45.0  # 增加内存限制，解决分配问题
    
    # GPU加速参数
    use_gpu: bool = True
    gpu_memory_limit_gb: float = 15.5  # 留0.5GB余量给系统
    gpu_batch_size: int = 1
    gpu_chunk_size: int = 8192  # GPU分块大小，用于超大图像
    
    # 图像保存参数
    jpeg_quality: int = 85
    save_format: str = "jpg"
    
    # 并行处理参数
    max_workers: int = 20


class Logger:
    """日志管理器"""
    
    def __init__(self, log_path: Optional[str] = None, enable_console: bool = True):
        self._logger = None
        self._setup_logging(log_path, enable_console)
    
    def _setup_logging(self, log_path: Optional[str], enable_console: bool) -> None:
        """配置日志记录器"""
        # 清理现有handlers
        root_logger = logging.getLogger()
        if root_logger.hasHandlers():
            root_logger.handlers.clear()
        
        handlers = []
        
        # 文件handler
        if log_path:
            file_handler = logging.FileHandler(log_path, mode='w', encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            handlers.append(file_handler)
        
        # 控制台handler  
        if enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            handlers.append(console_handler)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=handlers
        )
        
        self._logger = logging.getLogger(__name__)
    
    def info(self, message: str) -> None:
        """记录信息级别日志"""
        if self._logger:
            self._logger.info(message)
    
    def warning(self, message: str) -> None:
        """记录警告级别日志"""
        if self._logger:
            self._logger.warning(message)
    
    def error(self, message: str) -> None:
        """记录错误级别日志"""
        if self._logger:
            self._logger.error(message)
    
    def debug(self, message: str) -> None:
        """记录调试级别日志"""
        if self._logger:
            self._logger.debug(message)


class FeatureDetectorFactory:
    """特征检测器工厂"""
    
    @staticmethod
    def create_detector(detector_type: FeatureDetectorType, feature_count: int) -> Any:
        """创建特征检测器"""
        if detector_type == FeatureDetectorType.SIFT:
            return FeatureDetectorFactory._create_sift_detector(feature_count)
        elif detector_type == FeatureDetectorType.ORB:
            return FeatureDetectorFactory._create_orb_detector(feature_count)
        elif detector_type == FeatureDetectorType.AKAZE:
            return FeatureDetectorFactory._create_akaze_detector()
        else:
            raise ValueError(f"不支持的特征检测器类型: {detector_type}")
    
    @staticmethod
    def _create_sift_detector(feature_count: int) -> Any:
        """创建SIFT检测器"""
        try:
            return cv2.SIFT_create(nfeatures=feature_count)
        except AttributeError:
            try:
                return cv2.xfeatures2d.SIFT_create(nfeatures=feature_count)
            except (AttributeError, ImportError):
                raise RuntimeError("SIFT检测器不可用")
    
    @staticmethod
    def _create_orb_detector(feature_count: int) -> Any:
        """创建ORB检测器"""
        return cv2.ORB_create(nfeatures=feature_count)
    
    @staticmethod
    def _create_akaze_detector() -> Any:
        """创建AKAZE检测器"""
        return cv2.AKAZE_create()


class FeatureMatcherFactory:
    """特征匹配器工厂"""
    
    @staticmethod
    def create_matcher(detector_type: FeatureDetectorType) -> Any:
        """根据检测器类型创建匹配器"""
        if detector_type == FeatureDetectorType.ORB:
            return cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)
        elif detector_type in [FeatureDetectorType.SIFT, FeatureDetectorType.AKAZE]:
            try:
                FLANN_INDEX_KDTREE = 1
                index_params = {"algorithm": FLANN_INDEX_KDTREE, "trees": 5}
                search_params = {"checks": 50}
                return cv2.FlannBasedMatcher(index_params, search_params)
            except Exception:
                return cv2.BFMatcher(cv2.NORM_L2, crossCheck=False)
        else:
            return cv2.BFMatcher(cv2.NORM_L2, crossCheck=False)


class ConfigurationParser:
    """配置文件解析器"""
    
    @staticmethod
    def parse_tile_config(config_path: str, logger: Logger) -> Tuple[List[str], np.ndarray]:
        """解析TileConfiguration.txt文件"""
        filenames, positions = [], []
        pattern = re.compile(r'^(.*?)\s*;\s*;\s*\((.*?),(.*?)\)')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#') or 'dim =' in line: 
                        continue
                        
                    match = pattern.match(line)
                    if match:
                        filenames.append(match.group(1).strip())
                        x, y = float(match.group(2).strip()), float(match.group(3).strip())
                        positions.append([y, x])
                        
            logger.info(f"解析配置文件完成: 找到 {len(filenames)} 张图像")
            return filenames, np.array(positions, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"解析配置文件失败: {e}")
            raise


class ImageLoader:
    """图像加载器"""
    
    @staticmethod
    def load_images(image_dir: str, filenames: List[str], color: bool = False, logger: Optional[Logger] = None) -> List[np.ndarray]:
        """加载图像"""
        desc = "加载彩色图" if color else "加载灰度图"
        mode = cv2.IMREAD_COLOR if color else cv2.IMREAD_GRAYSCALE
        
        if logger:
            logger.info(f"正在{desc}像用于{'融合' if color else '配准'}...")
        
        images = []
        for filename in tqdm(filenames, desc=desc, ncols=80):
            img_path = os.path.join(image_dir, filename)
            img = cv2.imread(img_path, mode)
            if img is None:
                raise ValueError(f"无法加载图像: {img_path}")
            images.append(img)
        
        return images


class MemoryManager:
    """内存管理器"""
    
    @staticmethod
    def estimate_memory_usage(canvas_shape: np.ndarray, channels: int) -> float:
        """估算内存使用量（GB）"""
        # 计算画布内存
        canvas_memory = (canvas_shape[0] * canvas_shape[1] * channels * 4) / (1024**3)
        
        # 计算权重图内存
        weight_memory = (canvas_shape[0] * canvas_shape[1] * 4) / (1024**3)
        
        # 额外开销（缓冲区、临时变量等）
        overhead_factor = 1.5
        
        total_memory = (canvas_memory + weight_memory) * overhead_factor
        return total_memory
    
    @staticmethod
    def check_memory_availability(required_gb: float, limit_gb: float, logger: Logger) -> bool:
        """检查内存可用性"""
        logger.info(f"预估内存需求: {required_gb:.2f} GB")
        logger.info(f"内存限制: {limit_gb:.2f} GB")
        
        if required_gb > limit_gb:
            logger.warning(f"内存需求过大 ({required_gb:.2f} GB > {limit_gb} GB)")
            return False
        
        return True


class ImageLayoutAnalyzer:
    """图像布局分析器"""
    
    @staticmethod
    def analyze_layout(filenames: List[str], initial_positions: np.ndarray, logger: Logger) -> Tuple[Optional[int], Optional[int], Optional[int]]:
        """分析图像排列结构"""
        logger.info("=== 快速分析图像排列结构 ===")
        
        unique_y = sorted(list(set(np.round(initial_positions[:, 0], 0))))
        unique_x = sorted(list(set(np.round(initial_positions[:, 1], 0))))
        
        rows, cols = len(unique_y), len(unique_x)
        logger.info(f"网格结构: {rows} 行 x {cols} 列")
        
        if len(unique_y) > 1:
            y_spacing = np.mean(np.diff(unique_y))
            logger.info(f"Y方向间距: {y_spacing:.1f}")
        if len(unique_x) > 1:
            x_spacing = np.mean(np.diff(unique_x))
            logger.info(f"X方向间距: {x_spacing:.1f}")
        
        key_images = ["s_0001.jpg", "s_0002.jpg", "s_0032.jpg"]
        indices = []
        
        for key_image in key_images:
            try:
                idx = filenames.index(key_image)
                indices.append(idx)
            except ValueError:
                indices.append(None)
                
        return tuple(indices)
    
    @staticmethod
    def find_grid_neighbors(initial_positions: np.ndarray, logger: Logger) -> List[Tuple[int, int]]:
        """查找网格邻居"""
        unique_y = sorted(list(set(np.round(initial_positions[:, 0], 0))))
        unique_x = sorted(list(set(np.round(initial_positions[:, 1], 0))))
        rows, cols = len(unique_y), len(unique_x)
        
        pos_to_idx = {}
        for i, pos in enumerate(initial_positions):
            closest_y = min(unique_y, key=lambda y: abs(y - pos[0]))
            closest_x = min(unique_x, key=lambda x: abs(x - pos[1]))
            key = (closest_y, closest_x)
            pos_to_idx[key] = i
        
        neighbors = []
        for r in range(rows):
            for c in range(cols):
                current_key = (unique_y[r], unique_x[c])
                current_idx = pos_to_idx.get(current_key)
                if current_idx is None: 
                    continue
                
                directions = [(0, 1), (1, 0)]
                for dr, dc in directions:
                    nr, nc = r + dr, c + dc
                    if 0 <= nr < rows and 0 <= nc < cols:
                        neighbor_key = (unique_y[nr], unique_x[nc])
                        neighbor_idx = pos_to_idx.get(neighbor_key)
                        if neighbor_idx is not None:
                            neighbors.append((current_idx, neighbor_idx))
        
        logger.info(f"网格方法找到 {len(neighbors)} 个相邻图像对。")
        return neighbors


class ImageStitcher:
    """高性能图像拼接器 - 重构版本"""
    
    def __init__(self, config: Optional[StitchingConfig] = None):
        """
        初始化拼接器
        
        Args:
            config: 拼接配置参数，如果为None则使用默认配置
        """
        self.config = config or StitchingConfig()
        self.logger: Optional[Logger] = None
        self.detector: Optional[Any] = None
        self.matcher: Optional[Any] = None
        self.detector_type: Optional[FeatureDetectorType] = None
        
        # 配置OpenCV
        self._setup_opencv()
        
        # 创建特征组件
        self._create_feature_components()
    
    def _setup_opencv(self) -> None:
        """配置OpenCV设置"""
        cv2.ocl.setUseOpenCL(False)
    
    def _create_feature_components(self) -> None:
        """创建特征检测器和匹配器"""
        # 按优先级尝试创建检测器
        detector_types = [FeatureDetectorType.SIFT, FeatureDetectorType.ORB, FeatureDetectorType.AKAZE]
        
        for detector_type in detector_types:
            try:
                self.detector = FeatureDetectorFactory.create_detector(detector_type, self.config.feature_count)
                self.detector_type = detector_type
                self.matcher = FeatureMatcherFactory.create_matcher(detector_type)
                break
            except (RuntimeError, AttributeError):
                continue
        
        if self.detector is None:
            raise RuntimeError("无法创建任何特征检测器")
    
    def _setup_logging(self, log_path: Optional[str] = None) -> None:
        """设置日志记录器"""
        self.logger = Logger(log_path, enable_console=True)
        if self.logger and self.detector_type:
            self.logger.info(f"使用{self.detector_type.value}特征检测器")
    
    def calculate_expected_shift(self, pos1: np.ndarray, pos2: np.ndarray, 
                               img1: np.ndarray, img2: np.ndarray) -> np.ndarray:
        """基于像素坐标计算预期位移"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        dx = pos2[1] - pos1[1]
        dy = pos2[0] - pos1[0]
        
        if abs(dx) > abs(dy):
            if dx > 0:
                expected_shift = np.array([0.0, float(w1)])
            else:
                expected_shift = np.array([0.0, float(-w2)])
        else:
            if dy > 0:
                expected_shift = np.array([float(h1), 0.0])
            else:
                expected_shift = np.array([float(-h2), 0.0])
        
        return expected_shift
    
    def get_overlap_roi(self, img1: np.ndarray, img2: np.ndarray, 
                       pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Tuple[int, int], Tuple[int, int]]:
        """获取重叠区域ROI"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        dx = pos2[1] - pos1[1]
        dy = pos2[0] - pos1[0]
        
        if abs(dx) > abs(dy):
            if dx > 0:
                roi1 = img1[:, int(w1 * (1 - self.config.overlap_ratio)):]
                roi2 = img2[:, :int(w2 * self.config.overlap_ratio)]
                offset1 = (0, int(w1 * (1 - self.config.overlap_ratio)))
                offset2 = (0, 0)
            else:
                roi1 = img1[:, :int(w1 * self.config.overlap_ratio)]
                roi2 = img2[:, int(w2 * (1 - self.config.overlap_ratio)):]
                offset1 = (0, 0)
                offset2 = (0, int(w2 * (1 - self.config.overlap_ratio)))
        else:
            if dy > 0:
                roi1 = img1[int(h1 * (1 - self.config.overlap_ratio)):, :]
                roi2 = img2[:int(h2 * self.config.overlap_ratio), :]
                offset1 = (int(h1 * (1 - self.config.overlap_ratio)), 0)
                offset2 = (0, 0)
            else:
                roi1 = img1[:int(h1 * self.config.overlap_ratio), :]
                roi2 = img2[int(h2 * (1 - self.config.overlap_ratio)):, :]
                offset1 = (0, 0)
                offset2 = (int(h2 * (1 - self.config.overlap_ratio)), 0)
        
        return roi1, roi2, offset1, offset2
    
    def feature_based_registration(self, img1: np.ndarray, img2: np.ndarray, 
                                 pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, float]:
        """特征匹配配准"""
        try:
            roi1, roi2, offset1, offset2 = self.get_overlap_roi(img1, img2, pos1, pos2)
            
            if roi1.shape[0] < 30 or roi1.shape[1] < 30 or roi2.shape[0] < 30 or roi2.shape[1] < 30:
                return np.array([0.0, 0.0]), 0.0
            
            if self.detector is None:
                return np.array([0.0, 0.0]), 0.0
                
            kp1, des1 = self.detector.detectAndCompute(roi1, None)
            kp2, des2 = self.detector.detectAndCompute(roi2, None)
            
            if des1 is None or des2 is None or len(des1) < 5 or len(des2) < 5:
                return np.array([0.0, 0.0]), 0.0
            
            if self.matcher is None:
                return np.array([0.0, 0.0]), 0.0
                
            matches = self.matcher.knnMatch(des1, des2, k=2)
            
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
            
            if len(good_matches) < 8:
                return np.array([0.0, 0.0]), 0.0
                
            good_matches = sorted(good_matches, key=lambda x: x.distance)[:min(100, len(good_matches))]
            
            if len(good_matches) < 8:
                return np.array([0.0, 0.0]), 0.0
            
            pts1 = np.array([kp1[m.queryIdx].pt for m in good_matches])
            pts2 = np.array([kp2[m.trainIdx].pt for m in good_matches])
            
            pts1[:, 0] += offset1[1]
            pts1[:, 1] += offset1[0]
            pts2[:, 0] += offset2[1]
            pts2[:, 1] += offset2[0]
            
            shifts = pts1 - pts2
            
            if len(shifts) > 10:
                median_shift = np.median(shifts, axis=0)
                distances = np.linalg.norm(shifts - median_shift, axis=1)
                threshold = np.median(distances) * 1.2
                inliers = distances < threshold
                
                if np.sum(inliers) >= 10:
                    inlier_shifts = shifts[inliers]
                    final_shift = np.mean(inlier_shifts, axis=0)
                    confidence = float(np.sum(inliers)) / len(shifts)
                    return np.array([final_shift[1], final_shift[0]]), confidence
            
            return np.array([0.0, 0.0]), 0.0
            
        except Exception as e:
            return np.array([0.0, 0.0]), 0.0
    
    def _worker_feature_match(self, args: Tuple) -> Optional[Tuple]:
        """特征匹配工作函数"""
        i, j, img_i, img_j, pos_i, pos_j = args
        
        try:
            shift, confidence = self.feature_based_registration(img_i, img_j, pos_i, pos_j)
            
            if confidence >= self.config.min_confidence:
                return i, j, shift, confidence
            
            expected_shift = self.calculate_expected_shift(pos_i, pos_j, img_i, img_j)
            return i, j, expected_shift, self.config.fallback_confidence
            
        except Exception as e:
            try:
                expected_shift = self.calculate_expected_shift(pos_i, pos_j, img_i, img_j)
                return i, j, expected_shift, 0.01
            except:
                return None
    
    def find_pairwise_shifts(self, images: List[np.ndarray], neighbor_pairs: List[Tuple[int, int]], 
                           filenames: List[str], initial_positions: np.ndarray) -> List[Tuple]:
        """多线程特征匹配"""
        if self.logger:
            self.logger.info(f"开始超高速多线程特征配准...")
            self.logger.info(f"使用 {self.config.max_workers} 个线程处理 {len(neighbor_pairs)} 个图像对")
        
        tasks = [(i, j, images[i], images[j], initial_positions[i], initial_positions[j]) 
                 for i, j in neighbor_pairs]
        
        shifts = []
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            future_to_task = {executor.submit(self._worker_feature_match, task): task for task in tasks}
            
            for future in tqdm(as_completed(future_to_task), desc=f"超高速特征匹配({self.config.max_workers}线程)", total=len(tasks)):
                result = future.result()
                if result is not None:
                    shifts.append(result)
        
        if self.logger:
            self.logger.info(f"超高速成功计算 {len(shifts)} 个特征匹配位移。")
        return shifts
    
    def optimize_positions(self, num_images: int, initial_positions: np.ndarray, 
                         pairwise_shifts: List[Tuple], filenames: List[str]) -> np.ndarray:
        """全局位置优化"""
        if self.logger:
            self.logger.info("开始超高速全局位置优化...")
        
        if not pairwise_shifts:
            if self.logger:
                self.logger.warning("没有可用的位移进行优化")
            return initial_positions
        
        filtered_shifts = []
        for i, j, shift, corr in pairwise_shifts:
            shift_magnitude = np.linalg.norm(shift)
            if shift_magnitude < 5000:
                filtered_shifts.append((i, j, shift, corr))
        
        current_shifts = filtered_shifts
        if self.logger:
            self.logger.info(f"预过滤后保留 {len(current_shifts)} 个位移")
        
        current_positions = initial_positions.copy()
        
        for iteration in range(self.config.max_iterations):
            if len(current_shifts) == 0:
                break
            
            num_equations = len(current_shifts) * 2 + num_images * 2
            num_variables = num_images * 2
            
            A = np.zeros((num_equations, num_variables))
            b = np.zeros(num_equations)
            
            equation_idx = 0
            
            for i, j, measured_shift, weight in current_shifts:
                A[equation_idx, j*2] = 1
                A[equation_idx, i*2] = -1
                b[equation_idx] = measured_shift[0]
                if weight < 0.08:
                    A[equation_idx] *= 0.3
                    b[equation_idx] *= 0.3
                else:
                    weight_factor = np.sqrt(max(0.1, weight))
                    A[equation_idx] *= weight_factor
                    b[equation_idx] *= weight_factor
                equation_idx += 1
                
                A[equation_idx, j*2+1] = 1
                A[equation_idx, i*2+1] = -1
                b[equation_idx] = measured_shift[1]
                if weight < 0.08:
                    A[equation_idx] *= 0.3
                    b[equation_idx] *= 0.3
                else:
                    weight_factor = np.sqrt(max(0.1, weight))
                    A[equation_idx] *= weight_factor
                    b[equation_idx] *= weight_factor
                equation_idx += 1
            
            for i in range(num_images):
                A[equation_idx, i*2] = self.config.constraint_weight
                b[equation_idx] = initial_positions[i, 0] * self.config.constraint_weight
                equation_idx += 1
                
                A[equation_idx, i*2+1] = self.config.constraint_weight
                b[equation_idx] = initial_positions[i, 1] * self.config.constraint_weight
                equation_idx += 1
            
            try:
                x_new = np.linalg.lstsq(A, b, rcond=None)[0]
                new_positions = x_new.reshape((num_images, 2))
            except:
                if self.logger:
                    self.logger.warning(f"线性求解失败，迭代 {iteration + 1}")
                break
            
            position_changes = np.linalg.norm(new_positions - current_positions, axis=1)
            max_change = np.max(position_changes)
            
            if max_change > self.config.max_position_change:
                change_ratio = self.config.max_position_change / max_change
                new_positions = current_positions + (new_positions - current_positions) * change_ratio
                if self.logger:
                    self.logger.info(f"迭代 {iteration + 1}: 限制位置变化 {max_change:.2f} -> {self.config.max_position_change}")
            
            current_positions = new_positions
            
            if iteration % 5 == 0 or iteration >= self.config.max_iterations - 1:
                errors = []
                for idx, (i, j, measured_shift, weight) in enumerate(current_shifts):
                    predicted_shift = current_positions[j] - current_positions[i]
                    error_vec = predicted_shift - measured_shift
                    error_magnitude = np.linalg.norm(error_vec)
                    errors.append(error_magnitude)
                
                if errors:
                    max_error = max(errors)
                    avg_error = np.mean(errors)
                    max_error_idx = np.argmax(errors)
                    
                    if max_error > self.config.abs_threshold and (avg_error == 0 or max_error / avg_error > self.config.ratio_threshold):
                        removed_shift = current_shifts[max_error_idx]
                        if self.logger:
                            self.logger.info(f"移除异常位移: 图像 {removed_shift[0]}-{removed_shift[1]}, 误差 {max_error:.2f}")
                        del current_shifts[max_error_idx]
                        continue
                    
                    if max_error < self.config.abs_threshold:
                        if self.logger:
                            self.logger.info(f"优化完成: 最大误差 {max_error:.2f}, 平均误差 {avg_error:.2f}")
                        break
        
        current_positions -= current_positions[0]
        if self.logger:
            self.logger.info("超高速全局优化完成")
        return current_positions
    
    def stitch_images(self, image_dir: str, config_filename: str = "TileConfiguration.txt",
                     output_filename: Optional[str] = None, 
                     registered_filename: Optional[str] = None,
                     log_filename: Optional[str] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
        """
        执行图像拼接 - 主要接口
        
        Args:
            image_dir: 图像目录路径
            config_filename: 配置文件名
            output_filename: 输出图像文件名（可选）
            registered_filename: 注册坐标文件名（可选）
            log_filename: 日志文件名（可选）
            
        Returns:
            Tuple[拼接后的图像, 最终位置坐标]
        """
        # 设置默认文件名
        if output_filename is None:
            output_filename = os.path.join(image_dir, "stitched_result.jpg")
        if registered_filename is None:
            registered_filename = os.path.join(image_dir, "TileConfiguration.registered_python.txt")
        if log_filename is None:
            log_filename = os.path.join(image_dir, "stitching_log.txt")
        
        # 初始化日志
        self._setup_logging(log_filename)
        start_time = time.time()
        
        if self.logger:
            self.logger.info("=== 超高速图像拼接开始 ===")
            self.logger.info(f"使用线程数: {self.config.max_workers}")
            self.logger.info(f"特征点数量: {self.config.feature_count}")
        
        try:
            # 解析配置文件
            config_path = os.path.join(image_dir, config_filename)
            filenames, initial_positions = ConfigurationParser.parse_tile_config(config_path, self.logger)

            # 分析图像布局
            idx_001, idx_002, idx_032 = ImageLayoutAnalyzer.analyze_layout(filenames, initial_positions, self.logger)
            
            # 加载图像
            gray_images = ImageLoader.load_images(image_dir, filenames, False, self.logger)
            
            # 寻找邻居对
            all_neighbors = ImageLayoutAnalyzer.find_grid_neighbors(initial_positions, self.logger)
            
            # 特征匹配
            pairwise_shifts = self.find_pairwise_shifts(gray_images, all_neighbors, filenames, initial_positions)
            
            # 全局优化
            final_positions = self.optimize_positions(len(filenames), initial_positions, pairwise_shifts, filenames)
            
            # 保存注册结果
            ResultSaver.save_config(registered_filename, filenames, final_positions, self.logger)
            
            # 图像融合
            color_images = ImageLoader.load_images(image_dir, filenames, True, self.logger)
            blending_engine = BlendingEngine(self.config, self.logger)
            stitched_image = blending_engine.blend_gpu(color_images, final_positions)
            
            # 保存结果
            ResultSaver.save_result(stitched_image, output_filename, self.config, start_time, len(filenames), self.logger)

            return stitched_image, final_positions
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"拼接过程中发生错误: {e}")
            raise


class BlendingEngine:
    """图像融合引擎"""
    
    def __init__(self, config: StitchingConfig, logger: Logger):
        self.config = config
        self.logger = logger
    
    def create_weight_map_gpu(self, h: int, w: int, device: Any, max_feather_pixels: int) -> Any:
        """在GPU上创建高质量权重图"""
        if not PYTORCH_AVAILABLE or torch is None:
            raise RuntimeError("PyTorch不可用")
            
        y_indices = torch.arange(h, device=device).float().unsqueeze(1)
        x_indices = torch.arange(w, device=device).float().unsqueeze(0)
        
        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices
        
        dist_to_edge = torch.min(
            torch.min(dist_to_top, dist_to_bottom),
            torch.min(dist_to_left, dist_to_right)
        )
        
        feather_pixels = min(max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = torch.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = torch.clamp(weight_map, 0.1, 1.0)
            
            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = torch.ones((h, w), device=device)
        
        return weight_map
    
    def create_weight_map_cpu(self, h: int, w: int, max_feather_pixels: int) -> np.ndarray:
        """创建高质量权重图（CPU版本）"""
        y_indices, x_indices = np.ogrid[:h, :w]
        
        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices
        
        dist_to_edge = np.minimum(
            np.minimum(dist_to_top, dist_to_bottom),
            np.minimum(dist_to_left, dist_to_right)
        )
        
        feather_pixels = min(max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = np.clip(weight_map, 0.1, 1.0)
            
            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = np.ones((h, w), dtype=np.float32)
        
        return weight_map.astype(np.float32)
    
    def blend_gpu(self, images: List[np.ndarray], final_positions: np.ndarray) -> Optional[np.ndarray]:
        """GPU加速的图像融合算法"""
        self.logger.info("开始GPU加速图像融合...")
        
        if not PYTORCH_AVAILABLE or not torch.cuda.is_available() or not self.config.use_gpu:
            self.logger.warning("GPU不可用或已禁用，回退到优化的CPU融合")
            return self.blend_cpu(images, final_positions)
        
        final_positions_int = np.round(final_positions).astype(int)
        min_coords = np.min(final_positions_int, axis=0)
        final_positions_int -= min_coords
        
        if len(images[0].shape) == 3:
            h, w, c = images[0].shape
        else:
            h, w = images[0].shape
            c = 1
            images = [img.reshape(h, w, 1) if len(img.shape) == 2 else img for img in images]
        
        canvas_shape = np.max(final_positions_int, axis=0) + np.array([h, w])
        required_memory = MemoryManager.estimate_memory_usage(canvas_shape, c)
        
        self.logger.info(f"GPU融合画布大小: {canvas_shape[0]} x {canvas_shape[1]} x {c}")
        self.logger.info(f"预估GPU内存需求: {required_memory:.2f} GB")
        
        if required_memory > self.config.gpu_memory_limit_gb:
            self.logger.warning(f"GPU内存需求过大 ({required_memory:.2f} GB > {self.config.gpu_memory_limit_gb} GB)")
            self.logger.info("尝试GPU分块融合...")
            return self._blend_gpu_chunked(images, final_positions_int, canvas_shape, c)
        
        try:
            device = torch.device('cuda')
            
            if c == 1:
                canvas = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
                weight_sum = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
            else:
                canvas = torch.zeros((canvas_shape[0], canvas_shape[1], c), dtype=torch.float32, device=device)
                weight_sum = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
            
            self.logger.info(f"成功在GPU上分配内存: {required_memory:.2f} GB")
            
            batch_size = self.config.gpu_batch_size
            num_batches = (len(images) + batch_size - 1) // batch_size
            
            self.logger.info(f"使用批处理大小: {batch_size}, 总批次: {num_batches}")
            
            for batch_idx in tqdm(range(num_batches), desc="GPU批处理融合"):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(images))
                
                for i in range(start_idx, end_idx):
                    img = images[i]
                    img_y, img_x = final_positions_int[i]
                    img_h, img_w = img.shape[:2]
                    
                    y_end = min(img_y + img_h, canvas_shape[0])
                    x_end = min(img_x + img_w, canvas_shape[1])
                    actual_h = y_end - img_y
                    actual_w = x_end - img_x
                    
                    if actual_h <= 0 or actual_w <= 0:
                        continue
                    
                    img_roi = img[:actual_h, :actual_w].astype(np.float32)
                    if len(img_roi.shape) == 2 and c > 1:
                        img_roi = img_roi.reshape(actual_h, actual_w, 1)
                    
                    img_tensor = torch.from_numpy(img_roi).to(device)
                    weight_map = self.create_weight_map_gpu(actual_h, actual_w, device, self.config.max_feather_pixels)
                    
                    canvas_y1, canvas_x1 = img_y, img_x
                    canvas_y2, canvas_x2 = img_y + actual_h, img_x + actual_w
                    
                    if c == 1:
                        if len(img_tensor.shape) == 3:
                            img_tensor = img_tensor.squeeze(-1)
                        canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += img_tensor * weight_map
                    else:
                        for ch in range(min(c, img_tensor.shape[2] if len(img_tensor.shape) == 3 else 1)):
                            if len(img_tensor.shape) == 3:
                                canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_tensor[:, :, ch] * weight_map
                            else:
                                canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_tensor * weight_map
                    
                    weight_sum[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += weight_map
            
            weight_sum = torch.clamp(weight_sum, min=1e-10)
            
            if c == 1:
                canvas = canvas / weight_sum
            else:
                for ch in range(c):
                    canvas[:, :, ch] = canvas[:, :, ch] / weight_sum
            
            canvas = torch.clamp(canvas, 0, 255)
            result = canvas.byte().cpu().numpy()
            
            if c == 1:
                result = result.squeeze() if result.ndim > 2 else result
            
            self.logger.info("GPU加速图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"GPU融合失败: {e}")
            self.logger.info("回退到CPU融合")
            return self.blend_cpu(images, final_positions)
    
    def _blend_gpu_chunked(self, images: List[np.ndarray], final_positions_int: np.ndarray, 
                          canvas_shape: np.ndarray, c: int) -> Optional[np.ndarray]:
        """GPU分块融合（内存不足时的备选方案）"""
        self.logger.info("开始GPU分块融合...")
        
        if not PYTORCH_AVAILABLE or torch is None:
            self.logger.warning("PyTorch不可用，回退到CPU融合")
            return self.blend_cpu(images, final_positions_int + np.min(final_positions_int, axis=0))
        
        chunk_size = self.config.gpu_chunk_size
        device = torch.device('cuda')
        
        try:
            # 创建最终结果画布（在CPU上）
            if c == 1:
                result = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.uint8)
            else:
                result = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.uint8)
            
            total_chunks = ((canvas_shape[0] + chunk_size - 1) // chunk_size) * ((canvas_shape[1] + chunk_size - 1) // chunk_size)
            self.logger.info(f"GPU分块大小: {chunk_size}x{chunk_size}, 总块数: {total_chunks}")
            
            chunk_count = 0
            for y_start in tqdm(range(0, canvas_shape[0], chunk_size), desc="GPU分块融合"):
                y_end = min(y_start + chunk_size, canvas_shape[0])
                
                for x_start in range(0, canvas_shape[1], chunk_size):
                    x_end = min(x_start + chunk_size, canvas_shape[1])
                    chunk_count += 1
                    
                    chunk_h = y_end - y_start
                    chunk_w = x_end - x_start
                    
                    # 在GPU上创建块画布
                    if c == 1:
                        chunk_canvas = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    else:
                        chunk_canvas = torch.zeros((chunk_h, chunk_w, c), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    
                    # 处理与当前块重叠的图像
                    for i, img in enumerate(images):
                        img_y, img_x = final_positions_int[i]
                        img_h, img_w = img.shape[:2]
                        
                        # 检查图像是否与当前块重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 在图像中的坐标
                        img_x1 = overlap_x1 - img_x
                        img_y1 = overlap_y1 - img_y
                        img_x2 = overlap_x2 - img_x
                        img_y2 = overlap_y2 - img_y
                        
                        # 在块中的坐标
                        chunk_x1 = overlap_x1 - x_start
                        chunk_y1 = overlap_y1 - y_start
                        chunk_x2 = overlap_x2 - x_start
                        chunk_y2 = overlap_y2 - y_start
                        
                        img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        if len(img_roi.shape) == 2 and c > 1:
                            img_roi = img_roi.reshape(img_roi.shape[0], img_roi.shape[1], 1)
                        
                        # 转换到GPU
                        img_tensor = torch.from_numpy(img_roi).to(device)
                        weight_map = self.create_weight_map_gpu(img_h, img_w, device, self.config.max_feather_pixels)
                        weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                        
                        # 融合到块画布
                        if c == 1:
                            if len(img_tensor.shape) == 3:
                                img_tensor = img_tensor.squeeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += img_tensor * weight_roi
                        else:
                            for ch in range(min(c, img_tensor.shape[2] if len(img_tensor.shape) == 3 else 1)):
                                if len(img_tensor.shape) == 3:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor[:, :, ch] * weight_roi
                                else:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor * weight_roi
                        
                        chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_roi
                    
                    # 归一化块
                    chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                    
                    if c == 1:
                        chunk_result = chunk_canvas / chunk_weight
                    else:
                        chunk_result = torch.zeros_like(chunk_canvas)
                        for ch in range(c):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                    
                    chunk_result = torch.clamp(chunk_result, 0, 255)
                    chunk_final = chunk_result.byte().cpu().numpy()
                    
                    # 保存到最终结果
                    result[y_start:y_end, x_start:x_end] = chunk_final
                    
                    # 清理GPU内存
                    del chunk_canvas, chunk_weight, chunk_result, chunk_final
                    torch.cuda.empty_cache()
            
            self.logger.info("GPU分块图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"GPU分块融合失败: {e}")
            self.logger.info("回退到CPU融合")
            return self.blend_cpu(images, final_positions_int + np.min(final_positions_int, axis=0))
    
    def blend_cpu(self, images: List[np.ndarray], final_positions: np.ndarray) -> Optional[np.ndarray]:
        """优化的CPU图像融合算法"""
        self.logger.info("开始优化CPU图像融合...")
        
        final_positions_int = np.round(final_positions).astype(int)
        min_coords = np.min(final_positions_int, axis=0)
        final_positions_int -= min_coords
        
        if len(images[0].shape) == 3:
            h, w, c = images[0].shape
        else:
            h, w = images[0].shape
            c = 1
            images = [img.reshape(h, w, 1) if len(img.shape) == 2 else img for img in images]
        
        canvas_shape = np.max(final_positions_int, axis=0) + np.array([h, w])
        required_memory = MemoryManager.estimate_memory_usage(canvas_shape, c)
        
        self.logger.info(f"CPU融合画布大小: {canvas_shape[0]} x {canvas_shape[1]} x {c}")
        
        # 检查内存并处理分块
        if not MemoryManager.check_memory_availability(required_memory, self.config.memory_limit_gb, self.logger):
            self.logger.warning("尝试分块处理...")
            return self._blend_cpu_chunked(images, final_positions_int, canvas_shape, c)
        
        try:
            if c == 1:
                canvas = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
                weight_sum = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
            else:
                canvas = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.float32)
                weight_sum = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
            
            self.logger.info(f"成功分配CPU内存: {required_memory:.2f} GB")
            
            for i in tqdm(range(len(images)), desc="CPU融合处理"):
                img = images[i]
                img_y, img_x = final_positions_int[i]
                img_h, img_w = img.shape[:2]
                
                y_end = min(img_y + img_h, canvas_shape[0])
                x_end = min(img_x + img_w, canvas_shape[1])
                actual_h = y_end - img_y
                actual_w = x_end - img_x
                
                if actual_h <= 0 or actual_w <= 0:
                    continue
                
                img_roi = img[:actual_h, :actual_w].astype(np.float32)
                if len(img_roi.shape) == 2 and c > 1:
                    img_roi = img_roi.reshape(actual_h, actual_w, 1)
                
                weight_map = self.create_weight_map_cpu(actual_h, actual_w, self.config.max_feather_pixels)
                
                canvas_y1, canvas_x1 = img_y, img_x
                canvas_y2, canvas_x2 = img_y + actual_h, img_x + actual_w
                
                if c == 1:
                    if len(img_roi.shape) == 3:
                        img_roi = img_roi.squeeze(-1)
                    canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += img_roi * weight_map
                else:
                    for ch in range(min(c, img_roi.shape[2] if len(img_roi.shape) == 3 else 1)):
                        if len(img_roi.shape) == 3:
                            canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_roi[:, :, ch] * weight_map
                        else:
                            canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_roi * weight_map
                
                weight_sum[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += weight_map
            
            weight_sum = np.maximum(weight_sum, 1e-10)
            
            if c == 1:
                canvas /= weight_sum
            else:
                for ch in range(c):
                    canvas[:, :, ch] /= weight_sum
            
            result = np.clip(canvas, 0, 255).astype(np.uint8)
            
            if c == 1:
                result = result.squeeze() if result.ndim > 2 else result
            
            self.logger.info("优化CPU图像融合完成")
            return result
            
        except MemoryError as e:
            self.logger.error(f"CPU内存分配失败: {e}")
            self.logger.warning("尝试分块处理...")
            return self._blend_cpu_chunked(images, final_positions_int, canvas_shape, c)
    
    def _blend_cpu_chunked(self, images: List[np.ndarray], final_positions_int: np.ndarray, 
                          canvas_shape: np.ndarray, c: int) -> Optional[np.ndarray]:
        """分块CPU融合（内存不足时的备选方案）"""
        self.logger.info("开始分块CPU融合...")
        
        chunk_size = 4096  # 4K块大小
        
        try:
            if c == 1:
                result = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.uint8)
            else:
                result = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.uint8)
            
            for y_start in tqdm(range(0, canvas_shape[0], chunk_size), desc="分块处理"):
                y_end = min(y_start + chunk_size, canvas_shape[0])
                
                for x_start in range(0, canvas_shape[1], chunk_size):
                    x_end = min(x_start + chunk_size, canvas_shape[1])
                    
                    chunk_h = y_end - y_start
                    chunk_w = x_end - x_start
                    
                    if c == 1:
                        chunk_canvas = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                        chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                    else:
                        chunk_canvas = np.zeros((chunk_h, chunk_w, c), dtype=np.float32)
                        chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                    
                    for i, img in enumerate(images):
                        img_y, img_x = final_positions_int[i]
                        img_h, img_w = img.shape[:2]
                        
                        # 检查图像是否与当前块重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 在图像中的坐标
                        img_x1 = overlap_x1 - img_x
                        img_y1 = overlap_y1 - img_y
                        img_x2 = overlap_x2 - img_x
                        img_y2 = overlap_y2 - img_y
                        
                        # 在块中的坐标
                        chunk_x1 = overlap_x1 - x_start
                        chunk_y1 = overlap_y1 - y_start
                        chunk_x2 = overlap_x2 - x_start
                        chunk_y2 = overlap_y2 - y_start
                        
                        img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        if len(img_roi.shape) == 2 and c > 1:
                            img_roi = img_roi.reshape(img_roi.shape[0], img_roi.shape[1], 1)
                        
                        weight_map = self.create_weight_map_cpu(img_h, img_w, self.config.max_feather_pixels)
                        weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                        
                        if c == 1:
                            if len(img_roi.shape) == 3:
                                img_roi = img_roi.squeeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += img_roi * weight_roi
                        else:
                            for ch in range(min(c, img_roi.shape[2] if len(img_roi.shape) == 3 else 1)):
                                if len(img_roi.shape) == 3:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_roi[:, :, ch] * weight_roi
                                else:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_roi * weight_roi
                        
                        chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_roi
                    
                    # 归一化和保存块
                    chunk_weight = np.maximum(chunk_weight, 1e-10)
                    
                    if c == 1:
                        chunk_result = chunk_canvas / chunk_weight
                    else:
                        chunk_result = np.zeros_like(chunk_canvas)
                        for ch in range(c):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                    
                    chunk_result = np.clip(chunk_result, 0, 255).astype(np.uint8)
                    result[y_start:y_end, x_start:x_end] = chunk_result
            
            self.logger.info("分块CPU图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"分块融合失败: {e}")
            return None


class ResultSaver:
    """结果保存器"""
    
    @staticmethod
    def save_config(path: str, filenames: List[str], final_positions: np.ndarray, logger: Logger) -> None:
        """保存优化后的坐标到文件"""
        logger.info(f"正在将校正后坐标写入: {path}")
        with open(path, 'w', encoding='utf-8') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates\n")
            for i, filename in enumerate(filenames):
                y, x = final_positions[i]
                f.write(f"{filename}; ; ({x:.1f}, {y:.1f})\n")
        logger.info("坐标文件写入成功。")
    
    @staticmethod
    def save_result(stitched_image: Optional[np.ndarray], output_filename: str, 
                   config: StitchingConfig, start_time: float, num_images: int, logger: Logger) -> None:
        """保存最终结果并统计性能"""
        if stitched_image is not None:
            if config.save_format.lower() in ['jpg', 'jpeg']:
                cv2.imwrite(output_filename, stitched_image, [cv2.IMWRITE_JPEG_QUALITY, config.jpeg_quality])
                logger.info(f"拼接图像已保存至: {output_filename} (JPEG质量: {config.jpeg_quality}%)")
            elif config.save_format.lower() == 'png':
                compression_level = 6
                cv2.imwrite(output_filename, stitched_image, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
                logger.info(f"拼接图像已保存至: {output_filename} (PNG压缩级别: {compression_level})")
            elif config.save_format.lower() in ['tif', 'tiff']:
                cv2.imwrite(output_filename, stitched_image)
                logger.info(f"拼接图像已保存至: {output_filename} (TIFF无损格式)")
            else:
                cv2.imwrite(output_filename, stitched_image)
                logger.info(f"拼接图像已保存至: {output_filename}")
            
            try:
                file_size_bytes = os.path.getsize(output_filename)
                file_size_mb = file_size_bytes / (1024 * 1024)
                logger.info(f"文件大小: {file_size_mb:.1f} MB ({file_size_bytes:,} 字节)")
                
                if len(stitched_image.shape) == 3:
                    h, w, c = stitched_image.shape
                    total_pixels = h * w
                    logger.info(f"图像尺寸: {w} x {h} x {c} ({total_pixels:,} 像素)")
                else:
                    h, w = stitched_image.shape
                    total_pixels = h * w
                    logger.info(f"图像尺寸: {w} x {h} ({total_pixels:,} 像素)")
                    
                uncompressed_size_mb = (total_pixels * 3) / (1024 * 1024)
                compression_ratio = uncompressed_size_mb / file_size_mb if file_size_mb > 0 else 1
                logger.info(f"压缩比: {compression_ratio:.1f}:1 (未压缩: {uncompressed_size_mb:.1f} MB)")
                
            except Exception as e:
                logger.warning(f"无法获取文件大小信息: {e}")
        else:
            logger.info("由于内存限制，跳过了图像融合")
        
        total_time = time.time() - start_time
        logger.info(f"=== 任务完成！总耗时: {total_time:.2f} 秒 ===")
    
    def stitch_images(self, image_dir: str, config_filename: str = "TileConfiguration.txt",
                     output_filename: Optional[str] = None, 
                     registered_filename: Optional[str] = None,
                     log_filename: Optional[str] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
        """
        执行图像拼接 - 主要接口
        
        Args:
            image_dir: 图像目录路径
            config_filename: 配置文件名
            output_filename: 输出图像文件名（可选）
            registered_filename: 注册坐标文件名（可选）
            log_filename: 日志文件名（可选）
            
        Returns:
            Tuple[拼接后的图像, 最终位置坐标]
        """
        # 设置默认文件名
        if output_filename is None:
            output_filename = os.path.join(image_dir, "stitched_result.jpg")
        if registered_filename is None:
            registered_filename = os.path.join(image_dir, "TileConfiguration.registered_python.txt")
        if log_filename is None:
            log_filename = os.path.join(image_dir, "stitching_log.txt")
        
        # 初始化日志
        self._setup_logging(log_filename)
        start_time = time.time()
        
        if self.logger:
            self.logger.info("=== 超高速图像拼接开始 ===")
            self.logger.info(f"使用线程数: {self.config.max_workers}")
            self.logger.info(f"特征点数量: {self.config.feature_count}")
        
        try:
            # 解析配置文件
            config_path = os.path.join(image_dir, config_filename)
            filenames, initial_positions = ConfigurationParser.parse_tile_config(config_path, self.logger)

            # 分析图像布局
            idx_001, idx_002, idx_032 = ImageLayoutAnalyzer.analyze_layout(filenames, initial_positions, self.logger)
            
            # 加载图像
            gray_images = ImageLoader.load_images(image_dir, filenames, False, self.logger)
            
            # 寻找邻居对
            all_neighbors = ImageLayoutAnalyzer.find_grid_neighbors(initial_positions, self.logger)
            
            # 特征匹配
            pairwise_shifts = self.find_pairwise_shifts(gray_images, all_neighbors, filenames, initial_positions)
            
            # 全局优化
            final_positions = self.optimize_positions(len(filenames), initial_positions, pairwise_shifts, filenames)
            
            # 保存注册结果
            ResultSaver.save_config(registered_filename, filenames, final_positions, self.logger)
            
            # 图像融合
            color_images = ImageLoader.load_images(image_dir, filenames, True, self.logger)
            blending_engine = BlendingEngine(self.config, self.logger)
            stitched_image = blending_engine.blend_gpu(color_images, final_positions)
            
            # 保存结果
            ResultSaver.save_result(stitched_image, output_filename, self.config, start_time, len(filenames), self.logger)

            return stitched_image, final_positions
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"拼接过程中发生错误: {e}")
            raise


# ===== 便捷工厂函数 =====

def create_stitcher(feature_count: int = 800, max_workers: int = 20, 
                   memory_limit_gb: float = 45.0, use_gpu: bool = True) -> ImageStitcher:
    """
    创建图像拼接器的便捷工厂函数
    
    Args:
        feature_count: 特征点数量
        max_workers: 最大工作线程数
        memory_limit_gb: 内存限制（GB）
        use_gpu: 是否使用GPU加速
        
    Returns:
        配置好的ImageStitcher实例
    """
    config = StitchingConfig(
        feature_count=feature_count,
        max_workers=max_workers,
        memory_limit_gb=memory_limit_gb,
        use_gpu=use_gpu
    )
    return ImageStitcher(config)


def stitch_directory(image_dir: str, output_filename: Optional[str] = None, 
                    config: Optional[StitchingConfig] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
    """
    拼接目录中的图像的便捷函数
    
    Args:
        image_dir: 图像目录路径
        output_filename: 输出文件名（可选）
        config: 拼接配置（可选）
        
    Returns:
        Tuple[拼接后的图像, 最终位置坐标]
    """
    stitcher = ImageStitcher(config)
    return stitcher.stitch_images(image_dir, output_filename=output_filename)


# ======================== 程序入口点 ========================

if __name__ == '__main__':
    # 配置区域
    IMAGE_DIRECTORY = r"D:\\Image_SX88"
    CONFIG_FILE = "TileConfiguration.txt"
    REGISTERED_CONFIG_FILE = "TileConfiguration.registered_python.txt"
    LOG_FILE = "Log2.txt"
    OUTPUT_IMAGE_FILE = "Stitched_Image_SX88.jpg"
    
    # 创建拼接配置
    stitching_config = StitchingConfig(
        feature_count=1000,
        max_workers=12,
        overlap_ratio=0.1,
        min_confidence=0.1,
        max_iterations=20,
        abs_threshold=10.0,
        ratio_threshold=2.0,
        memory_limit_gb=32,  # 增加内存限制
        gpu_memory_limit_gb=3.5,  # 优化GPU内存限制，充分利用16GB显存
        gpu_batch_size=1,  # 减少批处理大小
        gpu_chunk_size=4096,  # GPU分块大小
        jpeg_quality=85,
        save_format="jpg"
    )
    
    # 验证输入
    if not os.path.isdir(IMAGE_DIRECTORY):
        print(f"错误: 找不到图像文件夹 '{IMAGE_DIRECTORY}'")
        sys.exit(1)
    
    # 准备路径
    multiprocessing.freeze_support()
    
    # 执行拼接
    try:
        # 方式1：使用类接口
        stitcher = ImageStitcher(stitching_config)
        stitched_image, final_positions = stitcher.stitch_images(
            IMAGE_DIRECTORY, CONFIG_FILE, OUTPUT_IMAGE_FILE, 
            REGISTERED_CONFIG_FILE, LOG_FILE
        )
        
        # 方式2：使用便捷函数（注释掉的替代方案）
        # stitched_image, final_positions = stitch_directory(IMAGE_DIRECTORY, OUTPUT_IMAGE_FILE, stitching_config)
        
    except Exception as e:
        print(f"拼接过程中发生错误: {e}")
        sys.exit(1)