"""
高性能图像拼接系统
优化的多线程图像拼接算法，支持大规模图像拼接
支持PyTorch GPU加速融合

重构版本：
- 清晰的模块化结构
- 解决logger初始化问题  
- 改进的类型注解
- 更好的错误处理
"""

import os
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from enum import Enum
from typing import List, Tuple, Optional, Dict, Any, Union, Protocol

# 解决OpenMP库冲突问题 - 在导入其他库之前设置
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import cv2
import numpy as np
import re
import time
import logging
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from skimage.registration import phase_cross_correlation
from scipy.optimize import least_squares
from scipy import ndimage
from scipy.ndimage import maximum_filter
from scipy.fft import fft2, ifft2, fftshift, ifftshift
from tqdm import tqdm
from dataclasses import dataclass, field

# PyTorch GPU支持
PYTORCH_AVAILABLE = False
torch = None
try:
    import torch
    import torch.nn.functional as F
    PYTORCH_AVAILABLE = True
    print(f"PyTorch可用: {torch.__version__}")
    if torch.cuda.is_available():
        print(f"CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"CUDA内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("CUDA不可用，将使用CPU模式")
except ImportError:
    print("PyTorch不可用，将使用传统CPU融合")


class FeatureDetectorType(Enum):
    """特征检测器类型枚举"""
    SIFT = "SIFT"
    ORB = "ORB"
    AKAZE = "AKAZE"


class SaveFormat(Enum):
    """保存格式枚举"""
    JPEG = "jpg"
    PNG = "png"
    TIFF = "tiff"


@dataclass
class StitchingConfig:
    """拼接算法配置参数"""
    # 特征匹配参数
    feature_count: int = 800
    overlap_ratio: float = 0.08
    min_confidence: float = 0.1
    fallback_confidence: float = 0.05
    
    # 全局优化参数
    max_iterations: int = 20
    abs_threshold: float = 10.0
    ratio_threshold: float = 2.0
    max_position_change: float = 30.0
    constraint_weight: float = 0.02
    
    # 融合参数
    feather_pixels_ratio: float = 0.1
    max_feather_pixels: int = 50
    min_feather_pixels: int = 5
    memory_limit_gb: float = 45.0  # 增加内存限制，解决分配问题
    
    # GPU加速参数
    use_gpu: bool = True
    gpu_memory_limit_gb: float = 15.5  # 留0.5GB余量给系统
    gpu_batch_size: int = 1
    gpu_chunk_size: int = 8192  # GPU分块大小，用于超大图像
    
    # 图像保存参数
    jpeg_quality: int = 85
    save_format: str = "jpg"
    
    # 并行处理参数
    max_workers: int = 20


class Logger:
    """日志管理器"""
    
    def __init__(self, log_path: Optional[str] = None, enable_console: bool = True):
        self._logger = None
        self._setup_logging(log_path, enable_console)
    
    def _setup_logging(self, log_path: Optional[str], enable_console: bool) -> None:
        """配置日志记录器"""
        # 清理现有handlers
        root_logger = logging.getLogger()
        if root_logger.hasHandlers():
            root_logger.handlers.clear()
        
        handlers = []
        
        # 文件handler
        if log_path:
            file_handler = logging.FileHandler(log_path, mode='w', encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            handlers.append(file_handler)
        
        # 控制台handler  
        if enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            handlers.append(console_handler)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=handlers
        )
        
        self._logger = logging.getLogger(__name__)
    
    def info(self, message: str) -> None:
        """记录信息级别日志"""
        if self._logger:
            self._logger.info(message)
    
    def warning(self, message: str) -> None:
        """记录警告级别日志"""
        if self._logger:
            self._logger.warning(message)
    
    def error(self, message: str) -> None:
        """记录错误级别日志"""
        if self._logger:
            self._logger.error(message)
    
    def debug(self, message: str) -> None:
        """记录调试级别日志"""
        if self._logger:
            self._logger.debug(message)


class FeatureDetectorFactory:
    """特征检测器工厂"""
    
    @staticmethod
    def create_detector(detector_type: FeatureDetectorType, feature_count: int) -> Any:
        """创建特征检测器"""
        if detector_type == FeatureDetectorType.SIFT:
            return FeatureDetectorFactory._create_sift_detector(feature_count)
        elif detector_type == FeatureDetectorType.ORB:
            return FeatureDetectorFactory._create_orb_detector(feature_count)
        elif detector_type == FeatureDetectorType.AKAZE:
            return FeatureDetectorFactory._create_akaze_detector()
        else:
            raise ValueError(f"不支持的特征检测器类型: {detector_type}")
    
    @staticmethod
    def _create_sift_detector(feature_count: int) -> Any:
        """创建SIFT检测器"""
        try:
            return cv2.SIFT_create(nfeatures=feature_count)
        except AttributeError:
            try:
                return cv2.xfeatures2d.SIFT_create(nfeatures=feature_count)
            except (AttributeError, ImportError):
                raise RuntimeError("SIFT检测器不可用")
    
    @staticmethod
    def _create_orb_detector(feature_count: int) -> Any:
        """创建ORB检测器"""
        return cv2.ORB_create(nfeatures=feature_count)
    
    @staticmethod
    def _create_akaze_detector() -> Any:
        """创建AKAZE检测器"""
        return cv2.AKAZE_create()


class FeatureMatcherFactory:
    """特征匹配器工厂"""
    
    @staticmethod
    def create_matcher(detector_type: FeatureDetectorType) -> Any:
        """根据检测器类型创建匹配器"""
        if detector_type == FeatureDetectorType.ORB:
            return cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)
        elif detector_type in [FeatureDetectorType.SIFT, FeatureDetectorType.AKAZE]:
            try:
                FLANN_INDEX_KDTREE = 1
                index_params = {"algorithm": FLANN_INDEX_KDTREE, "trees": 5}
                search_params = {"checks": 50}
                return cv2.FlannBasedMatcher(index_params, search_params)
            except Exception:
                return cv2.BFMatcher(cv2.NORM_L2, crossCheck=False)
        else:
            return cv2.BFMatcher(cv2.NORM_L2, crossCheck=False)


class ConfigurationParser:
    """配置文件解析器"""
    
    @staticmethod
    def parse_tile_config(config_path: str, logger: Logger) -> Tuple[List[str], np.ndarray]:
        """解析TileConfiguration.txt文件"""
        filenames, positions = [], []
        pattern = re.compile(r'^(.*?)\s*;\s*;\s*\((.*?),(.*?)\)')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#') or 'dim =' in line: 
                        continue
                        
                    match = pattern.match(line)
                    if match:
                        filenames.append(match.group(1).strip())
                        x, y = float(match.group(2).strip()), float(match.group(3).strip())
                        positions.append([y, x])
                        
            logger.info(f"解析配置文件完成: 找到 {len(filenames)} 张图像")
            return filenames, np.array(positions, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"解析配置文件失败: {e}")
            raise


class ImageLoader:
    """图像加载器"""
    
    @staticmethod
    def load_images(image_dir: str, filenames: List[str], color: bool = False, logger: Optional[Logger] = None) -> List[np.ndarray]:
        """加载图像"""
        desc = "加载彩色图" if color else "加载灰度图"
        mode = cv2.IMREAD_COLOR if color else cv2.IMREAD_GRAYSCALE
        
        if logger:
            logger.info(f"正在{desc}像用于{'融合' if color else '配准'}...")
        
        images = []
        for filename in tqdm(filenames, desc=desc, ncols=80):
            img_path = os.path.join(image_dir, filename)
            img = cv2.imread(img_path, mode)
            if img is None:
                raise ValueError(f"无法加载图像: {img_path}")
            images.append(img)
        
        return images


class MemoryManager:
    """内存管理器"""
    
    @staticmethod
    def estimate_memory_usage(canvas_shape: np.ndarray, channels: int) -> float:
        """估算内存使用量（GB）"""
        # 计算画布内存
        canvas_memory = (canvas_shape[0] * canvas_shape[1] * channels * 4) / (1024**3)
        
        # 计算权重图内存
        weight_memory = (canvas_shape[0] * canvas_shape[1] * 4) / (1024**3)
        
        # 额外开销（缓冲区、临时变量等）
        overhead_factor = 1.5
        
        total_memory = (canvas_memory + weight_memory) * overhead_factor
        return total_memory
    
    @staticmethod
    def check_memory_availability(required_gb: float, limit_gb: float, logger: Logger) -> bool:
        """检查内存可用性"""
        logger.info(f"预估内存需求: {required_gb:.2f} GB")
        logger.info(f"内存限制: {limit_gb:.2f} GB")
        
        if required_gb > limit_gb:
            logger.warning(f"内存需求过大 ({required_gb:.2f} GB > {limit_gb} GB)")
            return False
        
        return True


class ImageLayoutAnalyzer:
    """图像布局分析器"""
    
    @staticmethod
    def analyze_layout(filenames: List[str], initial_positions: np.ndarray, logger: Logger) -> Tuple[Optional[int], Optional[int], Optional[int]]:
        """分析图像排列结构"""
        logger.info("=== 快速分析图像排列结构 ===")
        
        unique_y = sorted(list(set(np.round(initial_positions[:, 0], 0))))
        unique_x = sorted(list(set(np.round(initial_positions[:, 1], 0))))
        
        rows, cols = len(unique_y), len(unique_x)
        logger.info(f"网格结构: {rows} 行 x {cols} 列")
        
        if len(unique_y) > 1:
            y_spacing = np.mean(np.diff(unique_y))
            logger.info(f"Y方向间距: {y_spacing:.1f}")
        if len(unique_x) > 1:
            x_spacing = np.mean(np.diff(unique_x))
            logger.info(f"X方向间距: {x_spacing:.1f}")
        
        key_images = ["s_0001.jpg", "s_0002.jpg", "s_0032.jpg"]
        indices = []
        
        for key_image in key_images:
            try:
                idx = filenames.index(key_image)
                indices.append(idx)
            except ValueError:
                indices.append(None)
                
        return tuple(indices)
    
    @staticmethod
    def find_grid_neighbors(initial_positions: np.ndarray, logger: Logger) -> List[Tuple[int, int]]:
        """查找网格邻居"""
        unique_y = sorted(list(set(np.round(initial_positions[:, 0], 0))))
        unique_x = sorted(list(set(np.round(initial_positions[:, 1], 0))))
        rows, cols = len(unique_y), len(unique_x)
        
        pos_to_idx = {}
        for i, pos in enumerate(initial_positions):
            closest_y = min(unique_y, key=lambda y: abs(y - pos[0]))
            closest_x = min(unique_x, key=lambda x: abs(x - pos[1]))
            key = (closest_y, closest_x)
            pos_to_idx[key] = i
        
        neighbors = []
        for r in range(rows):
            for c in range(cols):
                current_key = (unique_y[r], unique_x[c])
                current_idx = pos_to_idx.get(current_key)
                if current_idx is None: 
                    continue
                
                directions = [(0, 1), (1, 0)]
                for dr, dc in directions:
                    nr, nc = r + dr, c + dc
                    if 0 <= nr < rows and 0 <= nc < cols:
                        neighbor_key = (unique_y[nr], unique_x[nc])
                        neighbor_idx = pos_to_idx.get(neighbor_key)
                        if neighbor_idx is not None:
                            neighbors.append((current_idx, neighbor_idx))
        
        logger.info(f"网格方法找到 {len(neighbors)} 个相邻图像对。")
        return neighbors


class ImageStitcher:
    """高性能图像拼接器 - 重构版本"""
    
    def __init__(self, config: Optional[StitchingConfig] = None):
        """
        初始化拼接器
        
        Args:
            config: 拼接配置参数，如果为None则使用默认配置
        """
        self.config = config or StitchingConfig()
        self.logger: Optional[Logger] = None
        self.detector: Optional[Any] = None
        self.matcher: Optional[Any] = None
        self.detector_type: Optional[FeatureDetectorType] = None
        
        # 配置OpenCV
        self._setup_opencv()
        
        # 创建特征组件
        self._create_feature_components()
    
    def _setup_opencv(self) -> None:
        """配置OpenCV设置"""
        cv2.ocl.setUseOpenCL(False)
    
    def _create_feature_components(self) -> None:
        """创建特征检测器和匹配器 - 纯相位相关模式下不需要"""
        # 纯相位相关模式下不需要特征检测器
        self.detector = None
        self.detector_type = None
        self.matcher = None
    
    def _setup_logging(self, log_path: Optional[str] = None) -> None:
        """设置日志记录器"""
        self.logger = Logger(log_path, enable_console=True)
        if self.logger:
            self.logger.info("使用纯相位相关算法进行图像配准")
    
    def calculate_expected_shift(self, pos1: np.ndarray, pos2: np.ndarray,
                               img1: np.ndarray, img2: np.ndarray) -> np.ndarray:
        """基于像素坐标计算预期位移"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        dx = pos2[1] - pos1[1]
        dy = pos2[0] - pos1[0]

        if abs(dx) > abs(dy):
            if dx > 0:
                expected_shift = np.array([0.0, float(w1)])
            else:
                expected_shift = np.array([0.0, float(-w2)])
        else:
            if dy > 0:
                expected_shift = np.array([float(h1), 0.0])
            else:
                expected_shift = np.array([float(-h2), 0.0])

        return expected_shift

    def phase_correlation_registration(self, img1: np.ndarray, img2: np.ndarray,
                                     pos1: np.ndarray, pos2: np.ndarray,
                                     num_peaks: int = 5, subpixel_accuracy: bool = True) -> Tuple[np.ndarray, float]:
        """
        相位相关配准算法 - 基于ImageJ PhaseCorrelation实现

        Args:
            img1, img2: 输入图像
            pos1, pos2: 图像位置
            num_peaks: 检测的峰值数量
            subpixel_accuracy: 是否使用亚像素精度

        Returns:
            Tuple[位移向量, 相关系数] - img2相对于img1的位移
        """
        try:
            # 如果位置相同，直接对整个图像进行相位相关
            if np.allclose(pos1, pos2):
                # 确保图像尺寸一致
                h1, w1 = img1.shape[:2]
                h2, w2 = img2.shape[:2]
                min_h, min_w = min(h1, h2), min(w1, w2)

                roi1 = img1[:min_h, :min_w]
                roi2 = img2[:min_h, :min_w]
                offset_adjustment = np.array([0.0, 0.0])
            else:
                # 获取重叠区域ROI
                roi1, roi2, offset1, offset2 = self.get_overlap_roi(img1, img2, pos1, pos2)

                if roi1.shape[0] < 32 or roi1.shape[1] < 32 or roi2.shape[0] < 32 or roi2.shape[1] < 32:
                    return np.array([0.0, 0.0]), 0.0

                # 确保ROI尺寸一致
                min_h = min(roi1.shape[0], roi2.shape[0])
                min_w = min(roi1.shape[1], roi2.shape[1])
                roi1 = roi1[:min_h, :min_w]
                roi2 = roi2[:min_h, :min_w]

                # 计算偏移调整
                offset_adjustment = np.array([offset1[0] - offset2[0], offset1[1] - offset2[1]], dtype=np.float64)

            # 转换为灰度图（如果需要）
            if len(roi1.shape) == 3:
                roi1 = cv2.cvtColor(roi1, cv2.COLOR_BGR2GRAY)
            if len(roi2.shape) == 3:
                roi2 = cv2.cvtColor(roi2, cv2.COLOR_BGR2GRAY)

            # 转换为float64并归一化
            roi1_f = roi1.astype(np.float64) / 255.0
            roi2_f = roi2.astype(np.float64) / 255.0

            # 应用汉宁窗减少边缘效应
            roi1_f = self._apply_hanning_window(roi1_f)
            roi2_f = self._apply_hanning_window(roi2_f)

            # 零填充到合适的FFT尺寸
            roi1_padded, roi2_padded = self._zero_pad_to_fft_size(roi1_f, roi2_f)

            # 计算相位相关矩阵
            shift, correlation = self._compute_phase_correlation(roi1_padded, roi2_padded,
                                                               num_peaks, subpixel_accuracy)

            # 调整位移到原始图像坐标系
            # 注意：相位相关返回的是img2相对于img1的位移
            final_shift = shift + offset_adjustment

            return final_shift, correlation

        except Exception as e:
            if self.logger:
                self.logger.warning(f"相位相关配准失败: {e}")
            return np.array([0.0, 0.0]), 0.0

    def _apply_hanning_window(self, img: np.ndarray) -> np.ndarray:
        """应用汉宁窗减少边缘效应"""
        h, w = img.shape
        hanning_y = np.hanning(h).reshape(-1, 1)
        hanning_x = np.hanning(w).reshape(1, -1)
        window = hanning_y * hanning_x
        return img * window

    def _zero_pad_to_fft_size(self, img1: np.ndarray, img2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """零填充到合适的FFT尺寸"""
        h1, w1 = img1.shape
        h2, w2 = img2.shape

        # 计算合适的FFT尺寸（2的幂次）
        target_h = 1 << (max(h1, h2) - 1).bit_length()
        target_w = 1 << (max(w1, w2) - 1).bit_length()

        # 零填充
        padded1 = np.zeros((target_h, target_w), dtype=np.float32)
        padded2 = np.zeros((target_h, target_w), dtype=np.float32)

        padded1[:h1, :w1] = img1
        padded2[:h2, :w2] = img2

        return padded1, padded2

    def _compute_phase_correlation(self, img1: np.ndarray, img2: np.ndarray,
                                 num_peaks: int, subpixel_accuracy: bool) -> Tuple[np.ndarray, float]:
        """
        计算相位相关 - 基于傅里叶位移定理的精确实现

        理论基础：
        如果 f2(x,y) = f1(x-Δx, y-Δy)，则：
        F2(u,v) = F1(u,v) * exp(-j2π(uΔx + vΔy))

        互功率谱：R(u,v) = F1(u,v) * F2*(u,v) / |F1(u,v) * F2*(u,v)|
        逆FFT后得到：IFFT(R) = δ(x-Δx, y-Δy)
        """
        # 计算FFT - 使用双精度提高精度
        F1 = fft2(img1.astype(np.float64))
        F2 = fft2(img2.astype(np.float64))

        # 计算互功率谱 (Cross-Power Spectrum)
        # R(u,v) = F1(u,v) * F2*(u,v) / |F1(u,v) * F2*(u,v)|
        # 注意：这里计算的是img2相对于img1的位移
        cross_power_spectrum = F1 * np.conj(F2)

        # 计算幅度并进行归一化 - 这是关键步骤
        magnitude = np.abs(cross_power_spectrum)

        # 避免除零，但使用更小的阈值以保持精度
        magnitude[magnitude < 1e-15] = 1e-15

        # 归一化得到纯相位信息
        normalized_cross_power_spectrum = cross_power_spectrum / magnitude

        # 可选：应用低通滤波器减少高频噪声
        if hasattr(self.config, 'apply_lowpass_filter') and self.config.apply_lowpass_filter:
            normalized_cross_power_spectrum = self._apply_lowpass_filter(normalized_cross_power_spectrum)

        # 逆FFT得到相位相关结果
        correlation_result = np.real(ifft2(normalized_cross_power_spectrum))

        # 将零频率分量移到中心
        correlation_result = fftshift(correlation_result)

        # 寻找峰值
        peaks = self._find_correlation_peaks(correlation_result, num_peaks)

        if len(peaks) == 0:
            return np.array([0.0, 0.0]), 0.0

        # 选择最佳峰值（通常是最大值）
        best_peak = peaks[0]
        peak_value = correlation_result[best_peak[0], best_peak[1]]

        # 计算初始位移（相对于中心）
        center_y, center_x = correlation_result.shape[0] // 2, correlation_result.shape[1] // 2
        # 注意：相位相关的峰值位置直接对应位移
        shift = np.array([best_peak[0] - center_y, best_peak[1] - center_x], dtype=np.float64)

        # 亚像素精度定位
        if subpixel_accuracy and len(peaks) > 0:
            subpixel_shift = self._advanced_subpixel_localization(correlation_result, best_peak)
            shift = subpixel_shift - np.array([center_y, center_x], dtype=np.float64)

        # 计算归一化相关系数
        correlation_value = float(peak_value)

        # 验证结果的合理性
        if self._validate_correlation_result(shift, correlation_value, correlation_result.shape):
            return shift, correlation_value
        else:
            # 如果主峰不可信，尝试次优峰值
            if len(peaks) > 1:
                for i in range(1, min(len(peaks), 3)):
                    alt_peak = peaks[i]
                    alt_shift = np.array([alt_peak[0] - center_y, alt_peak[1] - center_x], dtype=np.float64)
                    alt_correlation = float(correlation_result[alt_peak[0], alt_peak[1]])

                    if self._validate_correlation_result(alt_shift, alt_correlation, correlation_result.shape):
                        if subpixel_accuracy:
                            alt_subpixel_shift = self._advanced_subpixel_localization(correlation_result, alt_peak)
                            alt_shift = alt_subpixel_shift - np.array([center_y, center_x], dtype=np.float64)
                        return alt_shift, alt_correlation

            # 如果所有峰值都不可信，返回零位移
            return np.array([0.0, 0.0]), 0.0

    def _apply_lowpass_filter(self, fft_data: np.ndarray) -> np.ndarray:
        """应用低通滤波器减少高频噪声"""
        h, w = fft_data.shape
        center_y, center_x = h // 2, w // 2

        # 创建高斯低通滤波器
        y, x = np.ogrid[:h, :w]
        distance_sq = (y - center_y)**2 + (x - center_x)**2

        # 截止频率设为图像尺寸的1/4
        cutoff_freq = min(h, w) / 8
        gaussian_filter = np.exp(-distance_sq / (2 * cutoff_freq**2))

        return fft_data * gaussian_filter

    def _validate_correlation_result(self, shift: np.ndarray, correlation: float,
                                   result_shape: Tuple[int, int]) -> bool:
        """验证相关结果的合理性"""
        # 检查位移是否在合理范围内
        max_shift = min(result_shape) // 4  # 最大位移不超过图像尺寸的1/4
        if np.linalg.norm(shift) > max_shift:
            return False

        # 检查相关系数是否足够高
        if correlation < 0.01:  # 最小相关阈值
            return False

        return True

    def _find_correlation_peaks(self, correlation_matrix: np.ndarray, num_peaks: int) -> List[Tuple[int, int]]:
        """
        寻找相关矩阵中的峰值 - 增强的鲁棒性实现

        包含以下鲁棒性增强：
        1. 噪声抑制
        2. 多峰值验证
        3. 异常检测
        """
        # 1. 噪声抑制 - 应用高斯滤波平滑相关矩阵
        from scipy.ndimage import gaussian_filter
        smoothed_matrix = gaussian_filter(correlation_matrix, sigma=0.8)

        # 2. 使用自适应阈值寻找局部最大值
        # 计算动态阈值
        mean_val = np.mean(smoothed_matrix)
        std_val = np.std(smoothed_matrix)
        threshold = mean_val + 2.0 * std_val  # 2-sigma阈值

        # 寻找局部最大值
        local_maxima = maximum_filter(smoothed_matrix, size=5) == smoothed_matrix

        # 应用阈值过滤
        significant_peaks = local_maxima & (smoothed_matrix > threshold)

        # 获取峰值位置和值
        peak_coords = np.where(significant_peaks)
        if len(peak_coords[0]) == 0:
            # 如果没有找到显著峰值，降低阈值重试
            threshold = mean_val + 1.0 * std_val
            significant_peaks = local_maxima & (smoothed_matrix > threshold)
            peak_coords = np.where(significant_peaks)

        if len(peak_coords[0]) == 0:
            # 如果仍然没有峰值，使用原始方法
            local_maxima = maximum_filter(correlation_matrix, size=3) == correlation_matrix
            peak_coords = np.where(local_maxima)

        peak_values = correlation_matrix[peak_coords]

        # 3. 多峰值验证 - 检查峰值的质量
        validated_peaks = []
        for i in range(len(peak_coords[0])):
            y, x = peak_coords[0][i], peak_coords[1][i]
            peak_value = peak_values[i]

            # 验证峰值质量
            if self._validate_peak_quality(correlation_matrix, (y, x), peak_value):
                validated_peaks.append((y, x, peak_value))

        # 按峰值大小排序
        validated_peaks.sort(key=lambda x: x[2], reverse=True)

        # 4. 异常检测 - 移除异常峰值
        filtered_peaks = self._filter_anomalous_peaks(validated_peaks, correlation_matrix.shape)

        # 返回前num_peaks个峰值
        peaks = [(y, x) for y, x, _ in filtered_peaks[:num_peaks]]

        return peaks

    def _validate_peak_quality(self, correlation_matrix: np.ndarray, peak: Tuple[int, int], peak_value: float) -> bool:
        """验证峰值质量"""
        y, x = peak
        h, w = correlation_matrix.shape

        # 检查峰值是否在边界附近（边界峰值通常不可靠）
        border_margin = min(h, w) // 10
        if (y < border_margin or y >= h - border_margin or
            x < border_margin or x >= w - border_margin):
            return False

        # 检查峰值的局部对比度
        if y > 0 and y < h-1 and x > 0 and x < w-1:
            neighborhood = correlation_matrix[y-1:y+2, x-1:x+2]
            local_mean = np.mean(neighborhood)
            local_std = np.std(neighborhood)

            # 峰值应该显著高于局部平均值
            if peak_value < local_mean + 1.5 * local_std:
                return False

        # 检查峰值的绝对强度
        matrix_max = np.max(correlation_matrix)
        if peak_value < 0.1 * matrix_max:  # 峰值应该至少是最大值的10%
            return False

        return True

    def _filter_anomalous_peaks(self, peaks: List[Tuple[int, int, float]],
                               matrix_shape: Tuple[int, int]) -> List[Tuple[int, int, float]]:
        """过滤异常峰值"""
        if len(peaks) <= 1:
            return peaks

        filtered_peaks = []
        center_y, center_x = matrix_shape[0] // 2, matrix_shape[1] // 2

        for i, (y, x, value) in enumerate(peaks):
            is_valid = True

            # 检查是否与其他峰值过于接近
            for j, (other_y, other_x, other_value) in enumerate(peaks):
                if i != j:
                    distance = np.sqrt((y - other_y)**2 + (x - other_x)**2)
                    if distance < 5:  # 峰值间距离应该大于5像素
                        # 保留较强的峰值
                        if value < other_value:
                            is_valid = False
                            break

            # 检查峰值位置的合理性
            distance_from_center = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            max_reasonable_distance = min(matrix_shape) // 3
            if distance_from_center > max_reasonable_distance:
                # 距离中心太远的峰值可能是噪声
                if value < 0.5 * peaks[0][2]:  # 且强度不够高
                    is_valid = False

            if is_valid:
                filtered_peaks.append((y, x, value))

        return filtered_peaks

    def _subpixel_localization(self, correlation_matrix: np.ndarray, peak: Tuple[int, int]) -> np.ndarray:
        """亚像素精度定位 - 基于ImageJ SubpixelLocalization"""
        y, x = peak
        h, w = correlation_matrix.shape

        # 确保峰值不在边界上
        if y <= 0 or y >= h-1 or x <= 0 or x >= w-1:
            return np.array([float(y), float(x)])

        # 使用二次插值进行亚像素定位
        try:
            # Y方向的二次拟合
            y_vals = correlation_matrix[y-1:y+2, x]
            if len(y_vals) == 3:
                a_y = (y_vals[0] + y_vals[2] - 2*y_vals[1]) / 2
                b_y = (y_vals[2] - y_vals[0]) / 2
                if abs(a_y) > 1e-10:
                    dy = -b_y / (2*a_y)
                    dy = np.clip(dy, -0.5, 0.5)  # 限制在合理范围内
                else:
                    dy = 0.0
            else:
                dy = 0.0

            # X方向的二次拟合
            x_vals = correlation_matrix[y, x-1:x+2]
            if len(x_vals) == 3:
                a_x = (x_vals[0] + x_vals[2] - 2*x_vals[1]) / 2
                b_x = (x_vals[2] - x_vals[0]) / 2
                if abs(a_x) > 1e-10:
                    dx = -b_x / (2*a_x)
                    dx = np.clip(dx, -0.5, 0.5)  # 限制在合理范围内
                else:
                    dx = 0.0
            else:
                dx = 0.0

            return np.array([float(y) + dy, float(x) + dx])

        except Exception:
            return np.array([float(y), float(x)])

    def _advanced_subpixel_localization(self, correlation_matrix: np.ndarray, peak: Tuple[int, int]) -> np.ndarray:
        """
        高精度亚像素定位 - 多种方法组合

        使用以下方法提高亚像素精度：
        1. 高斯拟合
        2. 抛物线插值
        3. 质心计算
        4. 加权平均
        """
        y, x = peak
        h, w = correlation_matrix.shape

        # 确保有足够的邻域进行拟合
        if y <= 1 or y >= h-2 or x <= 1 or x >= w-2:
            return np.array([float(y), float(x)])

        try:
            # 方法1: 高斯拟合
            gaussian_shift = self._gaussian_subpixel_fit(correlation_matrix, peak)

            # 方法2: 抛物线插值（改进版）
            parabolic_shift = self._parabolic_subpixel_fit(correlation_matrix, peak)

            # 方法3: 质心计算
            centroid_shift = self._centroid_subpixel_fit(correlation_matrix, peak)

            # 验证各方法的结果
            methods = [
                ("gaussian", gaussian_shift),
                ("parabolic", parabolic_shift),
                ("centroid", centroid_shift)
            ]

            valid_shifts = []
            for method_name, shift in methods:
                if self._validate_subpixel_shift(shift, peak):
                    valid_shifts.append(shift)

            if len(valid_shifts) == 0:
                # 如果所有方法都失败，回退到简单的二次插值
                return self._subpixel_localization(correlation_matrix, peak)
            elif len(valid_shifts) == 1:
                return valid_shifts[0]
            else:
                # 多个有效结果，使用加权平均
                weights = [1.0, 0.8, 0.6][:len(valid_shifts)]  # 高斯拟合权重最高
                weighted_sum = np.zeros(2)
                weight_sum = 0.0

                for i, shift in enumerate(valid_shifts):
                    weighted_sum += weights[i] * shift
                    weight_sum += weights[i]

                return weighted_sum / weight_sum

        except Exception:
            # 如果高级方法失败，回退到基本方法
            return self._subpixel_localization(correlation_matrix, peak)

    def _gaussian_subpixel_fit(self, correlation_matrix: np.ndarray, peak: Tuple[int, int]) -> np.ndarray:
        """高斯拟合亚像素定位"""
        y, x = peak

        # 提取3x3邻域
        neighborhood = correlation_matrix[y-1:y+2, x-1:x+2]

        # 转换为对数域进行线性拟合
        log_neighborhood = np.log(np.maximum(neighborhood, 1e-10))

        # 拟合二次曲面 z = ax² + by² + cxy + dx + ey + f
        # 使用最小二乘法求解
        Y, X = np.meshgrid(np.arange(-1, 2), np.arange(-1, 2), indexing='ij')
        points = np.column_stack([
            X.ravel()**2,  # x²
            Y.ravel()**2,  # y²
            (X*Y).ravel(), # xy
            X.ravel(),     # x
            Y.ravel(),     # y
            np.ones(9)     # 常数项
        ])

        try:
            coeffs = np.linalg.lstsq(points, log_neighborhood.ravel(), rcond=None)[0]
            a, b, c, d, e, f = coeffs

            # 计算峰值位置：∂z/∂x = 0, ∂z/∂y = 0
            # 2ax + cy + d = 0
            # 2by + cx + e = 0
            det = 4*a*b - c**2
            if abs(det) > 1e-10:
                dx = -(2*b*d - c*e) / det
                dy = -(2*a*e - c*d) / det

                # 限制在合理范围内
                dx = np.clip(dx, -0.5, 0.5)
                dy = np.clip(dy, -0.5, 0.5)

                return np.array([float(y) + dy, float(x) + dx])
        except:
            pass

        return np.array([float(y), float(x)])

    def _parabolic_subpixel_fit(self, correlation_matrix: np.ndarray, peak: Tuple[int, int]) -> np.ndarray:
        """改进的抛物线插值亚像素定位"""
        y, x = peak

        # 使用5点拟合提高精度
        try:
            # Y方向5点拟合
            if y >= 2 and y < correlation_matrix.shape[0] - 2:
                y_vals = correlation_matrix[y-2:y+3, x]
                # 使用加权最小二乘拟合
                weights = np.array([0.5, 1.0, 2.0, 1.0, 0.5])  # 中心点权重更高
                y_positions = np.arange(-2, 3)

                # 拟合二次多项式 y = ax² + bx + c
                A = np.column_stack([y_positions**2, y_positions, np.ones(5)])
                W = np.diag(weights)
                coeffs_y = np.linalg.lstsq(W @ A, W @ y_vals, rcond=None)[0]

                # 计算峰值位置
                a_y, b_y, c_y = coeffs_y
                if abs(a_y) > 1e-10:
                    dy = -b_y / (2 * a_y)
                    dy = np.clip(dy, -1.0, 1.0)
                else:
                    dy = 0.0
            else:
                dy = 0.0

            # X方向5点拟合
            if x >= 2 and x < correlation_matrix.shape[1] - 2:
                x_vals = correlation_matrix[y, x-2:x+3]
                weights = np.array([0.5, 1.0, 2.0, 1.0, 0.5])
                x_positions = np.arange(-2, 3)

                A = np.column_stack([x_positions**2, x_positions, np.ones(5)])
                W = np.diag(weights)
                coeffs_x = np.linalg.lstsq(W @ A, W @ x_vals, rcond=None)[0]

                a_x, b_x, c_x = coeffs_x
                if abs(a_x) > 1e-10:
                    dx = -b_x / (2 * a_x)
                    dx = np.clip(dx, -1.0, 1.0)
                else:
                    dx = 0.0
            else:
                dx = 0.0

            return np.array([float(y) + dy, float(x) + dx])

        except:
            return np.array([float(y), float(x)])

    def _centroid_subpixel_fit(self, correlation_matrix: np.ndarray, peak: Tuple[int, int]) -> np.ndarray:
        """质心计算亚像素定位"""
        y, x = peak

        # 使用5x5邻域计算加权质心
        size = 2
        y_start, y_end = max(0, y-size), min(correlation_matrix.shape[0], y+size+1)
        x_start, x_end = max(0, x-size), min(correlation_matrix.shape[1], x+size+1)

        neighborhood = correlation_matrix[y_start:y_end, x_start:x_end]

        # 确保所有值为正
        neighborhood = np.maximum(neighborhood, 0)

        if np.sum(neighborhood) == 0:
            return np.array([float(y), float(x)])

        # 计算质心
        Y, X = np.meshgrid(np.arange(y_start, y_end), np.arange(x_start, x_end), indexing='ij')

        total_weight = np.sum(neighborhood)
        centroid_y = np.sum(Y * neighborhood) / total_weight
        centroid_x = np.sum(X * neighborhood) / total_weight

        return np.array([centroid_y, centroid_x])

    def _validate_subpixel_shift(self, shift: np.ndarray, original_peak: Tuple[int, int]) -> bool:
        """验证亚像素位移的合理性"""
        if shift is None or len(shift) != 2:
            return False

        # 检查是否为有效数值
        if not np.all(np.isfinite(shift)):
            return False

        # 检查位移是否在合理范围内（距离原始峰值不超过1像素）
        original_pos = np.array([float(original_peak[0]), float(original_peak[1])])
        displacement = np.linalg.norm(shift - original_pos)

        return displacement <= 1.0
    
    def get_overlap_roi(self, img1: np.ndarray, img2: np.ndarray,
                       pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Tuple[int, int], Tuple[int, int]]:
        """
        获取重叠区域ROI - 基于规则扫描优化的精确提取策略

        根据TileConfiguration.txt分析和规则扫描先验知识：
        - 图像尺寸：2448 × 2048 像素（宽×高）
        - 横向间距：2203.2像素，重叠度：约10% (244.8像素)
        - 纵向间距：1843.2像素，重叠度：约10% (204.8像素)
        - 扫描模式：蛇形扫描，每张图像只与上下左右邻居重叠
        """
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        dx = pos2[1] - pos1[1]  # x方向位移
        dy = pos2[0] - pos1[0]  # y方向位移

        # 基于规则扫描的精确重叠区域计算
        if abs(dx) > abs(dy):  # 水平相邻
            # 使用已知的横向重叠度（约10%）
            expected_overlap_ratio = 0.10
            expected_overlap_width = int(w1 * expected_overlap_ratio)

            # 基于实际间距微调重叠宽度
            actual_overlap_width = max(0, w1 - abs(dx))

            # 选择更准确的重叠宽度
            if abs(actual_overlap_width - expected_overlap_width) < expected_overlap_width * 0.5:
                overlap_width = actual_overlap_width
            else:
                overlap_width = expected_overlap_width

            # 确保重叠区域在合理范围内
            overlap_width = max(int(w1 * 0.05), min(overlap_width, int(w1 * 0.4)))

            if dx > 0:  # img2在img1右侧
                # 扩展重叠区域以提高相位相关精度
                extended_overlap = min(int(overlap_width * 1.5), w1 // 3)

                roi1 = img1[:, w1-extended_overlap:]
                roi2 = img2[:, :extended_overlap]
                offset1 = (0, w1-extended_overlap)
                offset2 = (0, 0)
            else:  # img2在img1左侧
                extended_overlap = min(int(overlap_width * 1.5), w1 // 3)

                roi1 = img1[:, :extended_overlap]
                roi2 = img2[:, w2-extended_overlap:]
                offset1 = (0, 0)
                offset2 = (0, w2-extended_overlap)

        else:  # 垂直相邻
            # 使用已知的纵向重叠度（约10%）
            expected_overlap_ratio = 0.10
            expected_overlap_height = int(h1 * expected_overlap_ratio)

            # 基于实际间距微调重叠高度
            actual_overlap_height = max(0, h1 - abs(dy))

            # 选择更准确的重叠高度
            if abs(actual_overlap_height - expected_overlap_height) < expected_overlap_height * 0.5:
                overlap_height = actual_overlap_height
            else:
                overlap_height = expected_overlap_height

            # 确保重叠区域在合理范围内
            overlap_height = max(int(h1 * 0.05), min(overlap_height, int(h1 * 0.4)))

            if dy > 0:  # img2在img1下方
                # 扩展重叠区域以提高相位相关精度
                extended_overlap = min(int(overlap_height * 1.5), h1 // 3)

                roi1 = img1[h1-extended_overlap:, :]
                roi2 = img2[:extended_overlap, :]
                offset1 = (h1-extended_overlap, 0)
                offset2 = (0, 0)
            else:  # img2在img1上方
                extended_overlap = min(int(overlap_height * 1.5), h1 // 3)

                roi1 = img1[:extended_overlap, :]
                roi2 = img2[h2-extended_overlap:, :]
                offset1 = (0, 0)
                offset2 = (h2-extended_overlap, 0)

        # 验证ROI的有效性
        if roi1.shape[0] < 64 or roi1.shape[1] < 64 or roi2.shape[0] < 64 or roi2.shape[1] < 64:
            # 如果ROI太小，使用更大的区域
            return self._get_fallback_roi(img1, img2, pos1, pos2)

        return roi1, roi2, offset1, offset2

    def _get_fallback_roi(self, img1: np.ndarray, img2: np.ndarray,
                         pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Tuple[int, int], Tuple[int, int]]:
        """备用ROI提取策略，当主策略失败时使用"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        dx = pos2[1] - pos1[1]
        dy = pos2[0] - pos1[0]

        # 使用固定的30%重叠区域作为备用
        fallback_ratio = 0.3

        if abs(dx) > abs(dy):  # 水平相邻
            overlap_width = int(w1 * fallback_ratio)

            if dx > 0:  # img2在img1右侧
                roi1 = img1[:, w1-overlap_width:]
                roi2 = img2[:, :overlap_width]
                offset1 = (0, w1-overlap_width)
                offset2 = (0, 0)
            else:  # img2在img1左侧
                roi1 = img1[:, :overlap_width]
                roi2 = img2[:, w2-overlap_width:]
                offset1 = (0, 0)
                offset2 = (0, w2-overlap_width)
        else:  # 垂直相邻
            overlap_height = int(h1 * fallback_ratio)

            if dy > 0:  # img2在img1下方
                roi1 = img1[h1-overlap_height:, :]
                roi2 = img2[:overlap_height, :]
                offset1 = (h1-overlap_height, 0)
                offset2 = (0, 0)
            else:  # img2在img1上方
                roi1 = img1[:overlap_height, :]
                roi2 = img2[h2-overlap_height:, :]
                offset1 = (0, 0)
                offset2 = (h2-overlap_height, 0)

        return roi1, roi2, offset1, offset2
    
    def feature_based_registration(self, img1: np.ndarray, img2: np.ndarray, 
                                 pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, float]:
        """特征匹配配准"""
        try:
            roi1, roi2, offset1, offset2 = self.get_overlap_roi(img1, img2, pos1, pos2)
            
            if roi1.shape[0] < 30 or roi1.shape[1] < 30 or roi2.shape[0] < 30 or roi2.shape[1] < 30:
                return np.array([0.0, 0.0]), 0.0
            
            if self.detector is None:
                return np.array([0.0, 0.0]), 0.0
                
            kp1, des1 = self.detector.detectAndCompute(roi1, None)
            kp2, des2 = self.detector.detectAndCompute(roi2, None)
            
            if des1 is None or des2 is None or len(des1) < 5 or len(des2) < 5:
                return np.array([0.0, 0.0]), 0.0
            
            if self.matcher is None:
                return np.array([0.0, 0.0]), 0.0
                
            matches = self.matcher.knnMatch(des1, des2, k=2)
            
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
            
            if len(good_matches) < 8:
                return np.array([0.0, 0.0]), 0.0
                
            good_matches = sorted(good_matches, key=lambda x: x.distance)[:min(100, len(good_matches))]
            
            if len(good_matches) < 8:
                return np.array([0.0, 0.0]), 0.0
            
            pts1 = np.array([kp1[m.queryIdx].pt for m in good_matches])
            pts2 = np.array([kp2[m.trainIdx].pt for m in good_matches])
            
            pts1[:, 0] += offset1[1]
            pts1[:, 1] += offset1[0]
            pts2[:, 0] += offset2[1]
            pts2[:, 1] += offset2[0]
            
            shifts = pts1 - pts2
            
            if len(shifts) > 10:
                median_shift = np.median(shifts, axis=0)
                distances = np.linalg.norm(shifts - median_shift, axis=1)
                threshold = np.median(distances) * 1.2
                inliers = distances < threshold
                
                if np.sum(inliers) >= 10:
                    inlier_shifts = shifts[inliers]
                    final_shift = np.mean(inlier_shifts, axis=0)
                    confidence = float(np.sum(inliers)) / len(shifts)
                    return np.array([final_shift[1], final_shift[0]]), confidence
            
            return np.array([0.0, 0.0]), 0.0
            
        except Exception as e:
            return np.array([0.0, 0.0]), 0.0
    
    def _worker_pure_phase_correlation(self, args: Tuple) -> Optional[Tuple]:
        """纯相位相关配准工作函数 - 移除SIFT依赖"""
        i, j, img_i, img_j, pos_i, pos_j = args

        try:
            # 使用纯相位相关算法进行配准
            shift, correlation = self.pure_phase_correlation_registration(img_i, img_j, pos_i, pos_j)

            # 验证结果的合理性 - 降低阈值，相位相关的相关系数通常较低
            if correlation >= 0.01:  # 降低到0.01
                return i, j, shift, correlation

            # 如果相位相关失败，使用基于位置的预期位移
            expected_shift = self.calculate_expected_shift_from_positions(pos_i, pos_j)
            return i, j, expected_shift, 0.05

        except Exception as e:
            if self.logger:
                self.logger.warning(f"相位相关配准失败 ({i}-{j}): {e}")

            # 回退到基于位置的预期位移
            try:
                expected_shift = self.calculate_expected_shift_from_positions(pos_i, pos_j)
                return i, j, expected_shift, 0.01
            except:
                return None

    def pure_phase_correlation_registration(self, img1: np.ndarray, img2: np.ndarray,
                                          pos1: np.ndarray, pos2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        纯相位相关配准算法 - 基于傅里叶位移定理

        实现步骤：
        1. 确定邻居关系和重叠区域
        2. 提取重叠区域ROI
        3. 应用相位相关算法
        4. 寻找峰值点并计算位移

        Args:
            img1, img2: 输入图像
            pos1, pos2: 图像位置

        Returns:
            Tuple[位移向量, 相关系数]
        """
        try:
            # 确定图像的邻居关系
            dx = pos2[1] - pos1[1]  # x方向位移
            dy = pos2[0] - pos1[0]  # y方向位移

            # 提取重叠区域 - 基于规则扫描的先验知识
            if abs(dx) > abs(dy):  # 水平相邻
                roi1, roi2 = self._extract_horizontal_overlap_regions(img1, img2, dx > 0)
            else:  # 垂直相邻
                roi1, roi2 = self._extract_vertical_overlap_regions(img1, img2, dy > 0)

            # 验证ROI有效性
            if roi1.shape[0] < 32 or roi1.shape[1] < 32 or roi2.shape[0] < 32 or roi2.shape[1] < 32:
                return np.array([dy, dx]), 0.0

            # 应用相位相关算法
            shift, correlation = self._apply_phase_correlation(roi1, roi2)

            # 调整位移到全局坐标系
            if abs(dx) > abs(dy):  # 水平相邻
                # 水平位移主要体现在x方向
                final_shift = np.array([shift[0], shift[1]])
            else:  # 垂直相邻
                # 垂直位移主要体现在y方向
                final_shift = np.array([shift[0], shift[1]])

            return final_shift, correlation

        except Exception as e:
            if self.logger:
                self.logger.warning(f"纯相位相关配准失败: {e}")
            return np.array([dy, dx]), 0.0

    def _extract_horizontal_overlap_regions(self, img1: np.ndarray, img2: np.ndarray,
                                          img2_on_right: bool) -> Tuple[np.ndarray, np.ndarray]:
        """提取水平相邻图像的重叠区域"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        # 基于实际的重叠度（约10%）计算重叠区域
        # 使用更大的搜索区域以确保包含真实的重叠部分
        overlap_ratio = 0.25  # 使用25%的搜索区域
        overlap_width = int(min(w1, w2) * overlap_ratio)

        if img2_on_right:
            # img2在img1右侧
            # 从img1提取右侧区域作为模板
            roi1 = img1[:, w1-overlap_width:]
            # 从img2提取左侧区域作为目标
            roi2 = img2[:, :overlap_width]
        else:
            # img2在img1左侧
            roi1 = img1[:, :overlap_width]
            roi2 = img2[:, w2-overlap_width:]

        return roi1, roi2

    def _extract_vertical_overlap_regions(self, img1: np.ndarray, img2: np.ndarray,
                                        img2_below: bool) -> Tuple[np.ndarray, np.ndarray]:
        """提取垂直相邻图像的重叠区域"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]

        # 基于实际的重叠度（约10%）计算重叠区域
        overlap_ratio = 0.25  # 使用25%的搜索区域
        overlap_height = int(min(h1, h2) * overlap_ratio)

        if img2_below:
            # img2在img1下方
            # 从img1提取下方区域作为模板
            roi1 = img1[h1-overlap_height:, :]
            # 从img2提取上方区域作为目标
            roi2 = img2[:overlap_height, :]
        else:
            # img2在img1上方
            roi1 = img1[:overlap_height, :]
            roi2 = img2[h2-overlap_height:, :]

        return roi1, roi2

    def _apply_phase_correlation(self, roi1: np.ndarray, roi2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        应用相位相关算法 - 基于傅里叶位移定理的核心实现

        步骤：
        1. 计算FFT
        2. 计算互功率谱并归一化
        3. 逆FFT得到相关矩阵
        4. 寻找峰值点
        """
        # 转换为灰度图（如果需要）
        if len(roi1.shape) == 3:
            roi1 = cv2.cvtColor(roi1, cv2.COLOR_BGR2GRAY)
        if len(roi2.shape) == 3:
            roi2 = cv2.cvtColor(roi2, cv2.COLOR_BGR2GRAY)

        # 确保尺寸一致
        min_h = min(roi1.shape[0], roi2.shape[0])
        min_w = min(roi1.shape[1], roi2.shape[1])
        roi1 = roi1[:min_h, :min_w]
        roi2 = roi2[:min_h, :min_w]

        # 转换为float64并归一化
        f1 = roi1.astype(np.float64) / 255.0
        f2 = roi2.astype(np.float64) / 255.0

        # 步骤1: 计算FFT
        F1 = fft2(f1)
        F2 = fft2(f2)

        # 步骤2: 计算互功率谱并归一化
        # R(u,v) = F1(u,v) * F2*(u,v) / |F1(u,v) * F2*(u,v)|
        cross_power_spectrum = F1 * np.conj(F2)
        magnitude = np.abs(cross_power_spectrum)
        magnitude[magnitude < 1e-15] = 1e-15  # 避免除零

        # 归一化 - 这一步消除振幅信息，只保留相位差
        normalized_cross_power_spectrum = cross_power_spectrum / magnitude

        # 步骤3: 逆FFT得到相关矩阵
        # IFFT(R) = δ(x-Δx, y-Δy)
        correlation_matrix = np.real(ifft2(normalized_cross_power_spectrum))
        correlation_matrix = fftshift(correlation_matrix)

        # 步骤4: 寻找峰值点
        peak_y, peak_x = np.unravel_index(np.argmax(correlation_matrix), correlation_matrix.shape)

        # 计算位移（相对于中心）
        center_y, center_x = correlation_matrix.shape[0] // 2, correlation_matrix.shape[1] // 2
        shift_y = peak_y - center_y
        shift_x = peak_x - center_x

        # 修正位移方向 - 相位相关返回的是img2相对于img1的位移
        # 但我们需要的是img1到img2的位移，所以取负值
        shift_y = -shift_y
        shift_x = -shift_x

        # 计算相关系数
        correlation_value = correlation_matrix[peak_y, peak_x]

        return np.array([shift_y, shift_x]), float(correlation_value)

    def calculate_expected_shift_from_positions(self, pos1: np.ndarray, pos2: np.ndarray) -> np.ndarray:
        """基于位置计算预期位移"""
        return pos2 - pos1

    def _validate_shift_magnitude(self, shift: np.ndarray, pos1: np.ndarray, pos2: np.ndarray) -> bool:
        """验证位移大小的合理性"""
        # 计算预期位移
        expected_shift = pos2 - pos1

        # 检查位移是否在合理范围内（允许较大的误差，因为相位相关可能有偏差）
        max_error = 500  # 增加到500像素的允许误差
        error = np.linalg.norm(shift - expected_shift)

        return error <= max_error
    
    def find_pairwise_shifts(self, images: List[np.ndarray], neighbor_pairs: List[Tuple[int, int]],
                           filenames: List[str], initial_positions: np.ndarray) -> List[Tuple]:
        """多线程纯相位相关配准"""
        if self.logger:
            self.logger.info(f"开始纯相位相关配准...")
            self.logger.info(f"使用 {self.config.max_workers} 个线程处理 {len(neighbor_pairs)} 个图像对")

        tasks = [(i, j, images[i], images[j], initial_positions[i], initial_positions[j])
                 for i, j in neighbor_pairs]

        shifts = []

        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            future_to_task = {executor.submit(self._worker_pure_phase_correlation, task): task for task in tasks}

            for future in tqdm(as_completed(future_to_task), desc=f"纯相位相关配准({self.config.max_workers}线程)", total=len(tasks)):
                result = future.result()
                if result is not None:
                    shifts.append(result)

        if self.logger:
            self.logger.info(f"成功计算 {len(shifts)} 个纯相位相关位移。")
        return shifts
    
    def optimize_positions(self, num_images: int, initial_positions: np.ndarray,
                         pairwise_shifts: List[Tuple], filenames: List[str]) -> np.ndarray:
        """
        全局位置优化 - 基于ImageJ GlobalOptimization实现

        实现2-3轮迭代优化：
        1. 第一轮：基本优化，移除明显异常的匹配
        2. 第二轮：精细优化，调整权重
        3. 第三轮：最终优化，确保收敛
        """
        if self.logger:
            self.logger.info("开始全局位置优化（基于ImageJ GlobalOptimization）...")

        if not pairwise_shifts:
            if self.logger:
                self.logger.warning("没有可用的位移进行优化")
            return initial_positions

        # 预过滤异常位移
        filtered_shifts = []
        correlation_values = []

        for i, j, shift, corr in pairwise_shifts:
            shift_magnitude = np.linalg.norm(shift)
            correlation_values.append(corr)

            # 基于图像尺寸设置合理的位移阈值
            max_reasonable_shift = max(2448, 2048) * 0.5  # 最大半个图像尺寸

            # 降低相关系数阈值，因为相位相关的相关系数可能较低
            if shift_magnitude < max_reasonable_shift and corr > 0.001:  # 降低到0.001
                filtered_shifts.append((i, j, shift, corr))

        if self.logger:
            if correlation_values:
                min_corr = min(correlation_values)
                max_corr = max(correlation_values)
                avg_corr = sum(correlation_values) / len(correlation_values)
                self.logger.info(f"相关系数统计: 最小={min_corr:.6f}, 最大={max_corr:.6f}, 平均={avg_corr:.6f}")

            self.logger.info(f"预过滤后保留 {len(filtered_shifts)} 个位移")

        current_positions = initial_positions.copy()
        current_shifts = filtered_shifts.copy()

        # 多轮优化
        for round_num in range(3):  # 2-3轮优化
            if self.logger:
                self.logger.info(f"=== 第 {round_num + 1} 轮优化 ===")

            if len(current_shifts) == 0:
                break

            # 根据轮次调整参数
            if round_num == 0:
                # 第一轮：宽松参数，快速收敛
                max_iterations = 10
                error_threshold = 15.0
                weight_adjustment = 1.0
            elif round_num == 1:
                # 第二轮：中等参数，精细调整
                max_iterations = 15
                error_threshold = 10.0
                weight_adjustment = 1.2
            else:
                # 第三轮：严格参数，最终优化
                max_iterations = 20
                error_threshold = 8.0
                weight_adjustment = 1.5

            # 迭代优化
            for iteration in range(max_iterations):
                if len(current_shifts) == 0:
                    break

                # 构建线性方程组
                num_equations = len(current_shifts) * 2 + num_images * 2
                num_variables = num_images * 2

                A = np.zeros((num_equations, num_variables))
                b = np.zeros(num_equations)

                equation_idx = 0

                # 添加位移约束
                for i, j, measured_shift, weight in current_shifts:
                    # 调整权重
                    adjusted_weight = weight * weight_adjustment
                    if adjusted_weight < 0.1:
                        adjusted_weight *= 0.5  # 降低低质量匹配的权重
                    else:
                        adjusted_weight = np.sqrt(adjusted_weight)

                    # Y方向约束
                    A[equation_idx, j*2] = adjusted_weight
                    A[equation_idx, i*2] = -adjusted_weight
                    b[equation_idx] = measured_shift[0] * adjusted_weight
                    equation_idx += 1

                    # X方向约束
                    A[equation_idx, j*2+1] = adjusted_weight
                    A[equation_idx, i*2+1] = -adjusted_weight
                    b[equation_idx] = measured_shift[1] * adjusted_weight
                    equation_idx += 1

                # 添加正则化约束（保持接近初始位置）
                regularization_weight = self.config.constraint_weight * (0.5 ** round_num)
                for i in range(num_images):
                    A[equation_idx, i*2] = regularization_weight
                    b[equation_idx] = initial_positions[i, 0] * regularization_weight
                    equation_idx += 1

                    A[equation_idx, i*2+1] = regularization_weight
                    b[equation_idx] = initial_positions[i, 1] * regularization_weight
                    equation_idx += 1

                # 求解线性方程组
                try:
                    x_new = np.linalg.lstsq(A, b, rcond=None)[0]
                    new_positions = x_new.reshape((num_images, 2))
                except:
                    if self.logger:
                        self.logger.warning(f"第{round_num+1}轮第{iteration+1}次迭代线性求解失败")
                    break

                # 限制位置变化
                position_changes = np.linalg.norm(new_positions - current_positions, axis=1)
                max_change = np.max(position_changes)

                max_allowed_change = self.config.max_position_change * (2.0 ** round_num)
                if max_change > max_allowed_change:
                    change_ratio = max_allowed_change / max_change
                    new_positions = current_positions + (new_positions - current_positions) * change_ratio

                current_positions = new_positions

                # 检查收敛和移除异常匹配
                if iteration % 3 == 0 or iteration >= max_iterations - 1:
                    errors = []
                    for i, j, measured_shift, weight in current_shifts:
                        predicted_shift = current_positions[j] - current_positions[i]
                        error_vec = predicted_shift - measured_shift
                        error_magnitude = np.linalg.norm(error_vec)
                        errors.append(error_magnitude)

                    if errors:
                        max_error = max(errors)
                        avg_error = np.mean(errors)

                        # 移除异常匹配
                        if max_error > error_threshold and len(current_shifts) > num_images:
                            max_error_idx = np.argmax(errors)
                            removed_shift = current_shifts[max_error_idx]
                            if self.logger:
                                self.logger.info(f"第{round_num+1}轮移除异常位移: 图像 {removed_shift[0]}-{removed_shift[1]}, 误差 {max_error:.2f}")
                            del current_shifts[max_error_idx]
                            continue

                        # 检查收敛
                        if max_error < error_threshold * 0.7:
                            if self.logger:
                                self.logger.info(f"第{round_num+1}轮收敛: 最大误差 {max_error:.2f}, 平均误差 {avg_error:.2f}")
                            break

            if self.logger:
                remaining_errors = []
                for i, j, measured_shift, weight in current_shifts:
                    predicted_shift = current_positions[j] - current_positions[i]
                    error_magnitude = np.linalg.norm(predicted_shift - measured_shift)
                    remaining_errors.append(error_magnitude)

                if remaining_errors:
                    self.logger.info(f"第{round_num+1}轮完成: 剩余{len(current_shifts)}个匹配, "
                                   f"最大误差{max(remaining_errors):.2f}, 平均误差{np.mean(remaining_errors):.2f}")

        # 不要将第一个图像位置设为原点，保持与初始位置的相对关系
        # 这样可以保持与Fiji Stitching的坐标系统一致

        if self.logger:
            self.logger.info("全局位置优化完成")
        return current_positions
    
    def stitch_images(self, image_dir: str, config_filename: str = "TileConfiguration.txt",
                     output_filename: Optional[str] = None, 
                     registered_filename: Optional[str] = None,
                     log_filename: Optional[str] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
        """
        执行图像拼接 - 主要接口
        
        Args:
            image_dir: 图像目录路径
            config_filename: 配置文件名
            output_filename: 输出图像文件名（可选）
            registered_filename: 注册坐标文件名（可选）
            log_filename: 日志文件名（可选）
            
        Returns:
            Tuple[拼接后的图像, 最终位置坐标]
        """
        # 设置默认文件名
        if output_filename is None:
            output_filename = os.path.join(image_dir, "stitched_result.jpg")
        if registered_filename is None:
            registered_filename = os.path.join(image_dir, "TileConfiguration.registered_python.txt")
        if log_filename is None:
            log_filename = os.path.join(image_dir, "stitching_log.txt")
        
        # 初始化日志
        self._setup_logging(log_filename)
        start_time = time.time()
        
        if self.logger:
            self.logger.info("=== 超高速图像拼接开始 ===")
            self.logger.info(f"使用线程数: {self.config.max_workers}")
            self.logger.info(f"特征点数量: {self.config.feature_count}")
        
        try:
            # 解析配置文件
            config_path = os.path.join(image_dir, config_filename)
            filenames, initial_positions = ConfigurationParser.parse_tile_config(config_path, self.logger)

            # 分析图像布局
            idx_001, idx_002, idx_032 = ImageLayoutAnalyzer.analyze_layout(filenames, initial_positions, self.logger)
            
            # 加载图像
            gray_images = ImageLoader.load_images(image_dir, filenames, False, self.logger)
            
            # 寻找邻居对
            all_neighbors = ImageLayoutAnalyzer.find_grid_neighbors(initial_positions, self.logger)
            
            # 特征匹配
            pairwise_shifts = self.find_pairwise_shifts(gray_images, all_neighbors, filenames, initial_positions)
            
            # 全局优化
            final_positions = self.optimize_positions(len(filenames), initial_positions, pairwise_shifts, filenames)
            
            # 保存注册结果
            ResultSaver.save_config(registered_filename, filenames, final_positions, self.logger)
            
            # 图像融合
            color_images = ImageLoader.load_images(image_dir, filenames, True, self.logger)
            blending_engine = BlendingEngine(self.config, self.logger)
            stitched_image = blending_engine.blend_gpu(color_images, final_positions)
            
            # 保存结果
            ResultSaver.save_result(stitched_image, output_filename, self.config, start_time, len(filenames), self.logger)

            return stitched_image, final_positions
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"拼接过程中发生错误: {e}")
            raise


class BlendingEngine:
    """图像融合引擎"""
    
    def __init__(self, config: StitchingConfig, logger: Logger):
        self.config = config
        self.logger = logger
    
    def create_weight_map_gpu(self, h: int, w: int, device: Any, max_feather_pixels: int) -> Any:
        """在GPU上创建高质量权重图"""
        if not PYTORCH_AVAILABLE or torch is None:
            raise RuntimeError("PyTorch不可用")
            
        y_indices = torch.arange(h, device=device).float().unsqueeze(1)
        x_indices = torch.arange(w, device=device).float().unsqueeze(0)
        
        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices
        
        dist_to_edge = torch.min(
            torch.min(dist_to_top, dist_to_bottom),
            torch.min(dist_to_left, dist_to_right)
        )
        
        feather_pixels = min(max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)
        
        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = torch.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = torch.clamp(weight_map, 0.1, 1.0)
            
            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = torch.ones((h, w), device=device)
        
        return weight_map
    
    def create_weight_map_cpu(self, h: int, w: int, max_feather_pixels: int) -> np.ndarray:
        """创建高质量权重图（CPU版本）"""
        y_indices, x_indices = np.ogrid[:h, :w]

        dist_to_top = y_indices
        dist_to_bottom = h - 1 - y_indices
        dist_to_left = x_indices
        dist_to_right = w - 1 - x_indices

        dist_to_edge = np.minimum(
            np.minimum(dist_to_top, dist_to_bottom),
            np.minimum(dist_to_left, dist_to_right)
        )

        feather_pixels = min(max_feather_pixels, min(h, w) // 10)
        feather_pixels = max(feather_pixels, 5)

        if feather_pixels > 5:
            sigma = feather_pixels / 3.0
            weight_map = np.exp(-((feather_pixels - dist_to_edge) ** 2) / (2 * sigma ** 2))
            weight_map = np.clip(weight_map, 0.1, 1.0)

            inner_mask = dist_to_edge >= feather_pixels
            weight_map[inner_mask] = 1.0
        else:
            weight_map = np.ones((h, w), dtype=np.float32)

        return weight_map.astype(np.float32)

    def create_linear_blend_weight_map(self, h: int, w: int, overlap_info: Optional[Dict] = None) -> np.ndarray:
        """
        创建线性融合权重图 - 改进版本，特别优化垂直方向融合

        Args:
            h, w: 图像尺寸
            overlap_info: 重叠信息，包含重叠方向和区域

        Returns:
            权重图，范围[0, 1]
        """
        # 基础距离权重
        y_indices, x_indices = np.ogrid[:h, :w]

        # 计算到各边缘的距离
        dist_to_top = y_indices.astype(np.float32)
        dist_to_bottom = (h - 1 - y_indices).astype(np.float32)
        dist_to_left = x_indices.astype(np.float32)
        dist_to_right = (w - 1 - x_indices).astype(np.float32)

        # 改进的权重计算 - 特别优化垂直方向
        # 使用更平滑的权重分布，避免垂直方向的接缝

        # 计算到最近边缘的距离
        dist_to_edge = np.minimum(
            np.minimum(dist_to_top, dist_to_bottom),
            np.minimum(dist_to_left, dist_to_right)
        )

        # 使用平方根函数创建更平滑的权重分布
        max_dist = min(h, w) / 2.0
        normalized_dist = np.clip(dist_to_edge / max_dist, 0.0, 1.0)

        # 应用平方根函数，使权重变化更平滑
        weight_map = np.sqrt(normalized_dist)

        # 确保边缘有一定的羽化
        feather_pixels = min(50, min(h, w) // 20)
        if feather_pixels > 0:
            # 边缘羽化
            edge_mask = ((y_indices < feather_pixels) | (y_indices >= h - feather_pixels) |
                        (x_indices < feather_pixels) | (x_indices >= w - feather_pixels))

            # 计算到最近边缘的距离
            dist_to_edge = np.minimum(
                np.minimum(dist_to_top, dist_to_bottom),
                np.minimum(dist_to_left, dist_to_right)
            )

            # 在边缘区域应用线性衰减
            edge_weight = np.clip(dist_to_edge / feather_pixels, 0.0, 1.0)
            weight_map = np.where(edge_mask,
                                 weight_map * edge_weight,
                                 weight_map)

        # 确保权重在合理范围内
        weight_map = np.clip(weight_map, 0.05, 1.0)

        return weight_map.astype(np.float32)
    
    def blend_gpu(self, images: List[np.ndarray], final_positions: np.ndarray) -> Optional[np.ndarray]:
        """GPU加速的图像融合算法"""
        self.logger.info("开始GPU加速图像融合...")
        
        if not PYTORCH_AVAILABLE or not torch.cuda.is_available() or not self.config.use_gpu:
            self.logger.warning("GPU不可用或已禁用，回退到优化的CPU融合")
            return self.blend_cpu(images, final_positions)
        
        final_positions_int = np.round(final_positions).astype(int)
        min_coords = np.min(final_positions_int, axis=0)
        final_positions_int -= min_coords
        
        if len(images[0].shape) == 3:
            h, w, c = images[0].shape
        else:
            h, w = images[0].shape
            c = 1
            images = [img.reshape(h, w, 1) if len(img.shape) == 2 else img for img in images]
        
        canvas_shape = np.max(final_positions_int, axis=0) + np.array([h, w])
        required_memory = MemoryManager.estimate_memory_usage(canvas_shape, c)
        
        self.logger.info(f"GPU融合画布大小: {canvas_shape[0]} x {canvas_shape[1]} x {c}")
        self.logger.info(f"预估GPU内存需求: {required_memory:.2f} GB")
        
        if required_memory > self.config.gpu_memory_limit_gb:
            self.logger.warning(f"GPU内存需求过大 ({required_memory:.2f} GB > {self.config.gpu_memory_limit_gb} GB)")
            self.logger.info("尝试GPU分块融合...")
            return self._blend_gpu_chunked(images, final_positions_int, canvas_shape, c)
        
        try:
            device = torch.device('cuda')
            
            if c == 1:
                canvas = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
                weight_sum = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
            else:
                canvas = torch.zeros((canvas_shape[0], canvas_shape[1], c), dtype=torch.float32, device=device)
                weight_sum = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
            
            self.logger.info(f"成功在GPU上分配内存: {required_memory:.2f} GB")
            
            batch_size = self.config.gpu_batch_size
            num_batches = (len(images) + batch_size - 1) // batch_size
            
            self.logger.info(f"使用批处理大小: {batch_size}, 总批次: {num_batches}")
            
            for batch_idx in tqdm(range(num_batches), desc="GPU批处理融合"):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(images))
                
                for i in range(start_idx, end_idx):
                    img = images[i]
                    img_y, img_x = final_positions_int[i]
                    img_h, img_w = img.shape[:2]
                    
                    y_end = min(img_y + img_h, canvas_shape[0])
                    x_end = min(img_x + img_w, canvas_shape[1])
                    actual_h = y_end - img_y
                    actual_w = x_end - img_x
                    
                    if actual_h <= 0 or actual_w <= 0:
                        continue
                    
                    img_roi = img[:actual_h, :actual_w].astype(np.float32)
                    if len(img_roi.shape) == 2 and c > 1:
                        img_roi = img_roi.reshape(actual_h, actual_w, 1)
                    
                    img_tensor = torch.from_numpy(img_roi).to(device)
                    # 使用线性融合权重图
                    weight_map_cpu = self.create_linear_blend_weight_map(actual_h, actual_w)
                    weight_map = torch.from_numpy(weight_map_cpu).to(device)
                    
                    canvas_y1, canvas_x1 = img_y, img_x
                    canvas_y2, canvas_x2 = img_y + actual_h, img_x + actual_w
                    
                    if c == 1:
                        if len(img_tensor.shape) == 3:
                            img_tensor = img_tensor.squeeze(-1)
                        canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += img_tensor * weight_map
                    else:
                        for ch in range(min(c, img_tensor.shape[2] if len(img_tensor.shape) == 3 else 1)):
                            if len(img_tensor.shape) == 3:
                                canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_tensor[:, :, ch] * weight_map
                            else:
                                canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_tensor * weight_map
                    
                    weight_sum[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += weight_map
            
            weight_sum = torch.clamp(weight_sum, min=1e-10)
            
            if c == 1:
                canvas = canvas / weight_sum
            else:
                for ch in range(c):
                    canvas[:, :, ch] = canvas[:, :, ch] / weight_sum
            
            canvas = torch.clamp(canvas, 0, 255)
            result = canvas.byte().cpu().numpy()
            
            if c == 1:
                result = result.squeeze() if result.ndim > 2 else result
            
            self.logger.info("GPU加速图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"GPU融合失败: {e}")
            self.logger.info("回退到CPU融合")
            return self.blend_cpu(images, final_positions)
    
    def _blend_gpu_chunked(self, images: List[np.ndarray], final_positions_int: np.ndarray, 
                          canvas_shape: np.ndarray, c: int) -> Optional[np.ndarray]:
        """GPU分块融合（内存不足时的备选方案）"""
        self.logger.info("开始GPU分块融合...")
        
        if not PYTORCH_AVAILABLE or torch is None:
            self.logger.warning("PyTorch不可用，回退到CPU融合")
            return self.blend_cpu(images, final_positions_int + np.min(final_positions_int, axis=0))
        
        chunk_size = self.config.gpu_chunk_size
        device = torch.device('cuda')
        
        try:
            # 创建最终结果画布（在CPU上）
            if c == 1:
                result = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.uint8)
            else:
                result = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.uint8)
            
            total_chunks = ((canvas_shape[0] + chunk_size - 1) // chunk_size) * ((canvas_shape[1] + chunk_size - 1) // chunk_size)
            self.logger.info(f"GPU分块大小: {chunk_size}x{chunk_size}, 总块数: {total_chunks}")
            
            chunk_count = 0
            for y_start in tqdm(range(0, canvas_shape[0], chunk_size), desc="GPU分块融合"):
                y_end = min(y_start + chunk_size, canvas_shape[0])
                
                for x_start in range(0, canvas_shape[1], chunk_size):
                    x_end = min(x_start + chunk_size, canvas_shape[1])
                    chunk_count += 1
                    
                    chunk_h = y_end - y_start
                    chunk_w = x_end - x_start
                    
                    # 在GPU上创建块画布
                    if c == 1:
                        chunk_canvas = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    else:
                        chunk_canvas = torch.zeros((chunk_h, chunk_w, c), dtype=torch.float32, device=device)
                        chunk_weight = torch.zeros((chunk_h, chunk_w), dtype=torch.float32, device=device)
                    
                    # 处理与当前块重叠的图像
                    for i, img in enumerate(images):
                        img_y, img_x = final_positions_int[i]
                        img_h, img_w = img.shape[:2]
                        
                        # 检查图像是否与当前块重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 在图像中的坐标
                        img_x1 = overlap_x1 - img_x
                        img_y1 = overlap_y1 - img_y
                        img_x2 = overlap_x2 - img_x
                        img_y2 = overlap_y2 - img_y
                        
                        # 在块中的坐标
                        chunk_x1 = overlap_x1 - x_start
                        chunk_y1 = overlap_y1 - y_start
                        chunk_x2 = overlap_x2 - x_start
                        chunk_y2 = overlap_y2 - y_start
                        
                        img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        if len(img_roi.shape) == 2 and c > 1:
                            img_roi = img_roi.reshape(img_roi.shape[0], img_roi.shape[1], 1)
                        
                        # 转换到GPU
                        img_tensor = torch.from_numpy(img_roi).to(device)
                        weight_map = self.create_weight_map_gpu(img_h, img_w, device, self.config.max_feather_pixels)
                        weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                        
                        # 融合到块画布
                        if c == 1:
                            if len(img_tensor.shape) == 3:
                                img_tensor = img_tensor.squeeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += img_tensor * weight_roi
                        else:
                            for ch in range(min(c, img_tensor.shape[2] if len(img_tensor.shape) == 3 else 1)):
                                if len(img_tensor.shape) == 3:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor[:, :, ch] * weight_roi
                                else:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_tensor * weight_roi
                        
                        chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_roi
                    
                    # 归一化块
                    chunk_weight = torch.clamp(chunk_weight, min=1e-10)
                    
                    if c == 1:
                        chunk_result = chunk_canvas / chunk_weight
                    else:
                        chunk_result = torch.zeros_like(chunk_canvas)
                        for ch in range(c):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                    
                    chunk_result = torch.clamp(chunk_result, 0, 255)
                    chunk_final = chunk_result.byte().cpu().numpy()
                    
                    # 保存到最终结果
                    result[y_start:y_end, x_start:x_end] = chunk_final
                    
                    # 清理GPU内存
                    del chunk_canvas, chunk_weight, chunk_result, chunk_final
                    torch.cuda.empty_cache()
            
            self.logger.info("GPU分块图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"GPU分块融合失败: {e}")
            self.logger.info("回退到CPU融合")
            return self.blend_cpu(images, final_positions_int + np.min(final_positions_int, axis=0))
    
    def blend_cpu(self, images: List[np.ndarray], final_positions: np.ndarray) -> Optional[np.ndarray]:
        """优化的CPU图像融合算法"""
        self.logger.info("开始优化CPU图像融合...")
        
        final_positions_int = np.round(final_positions).astype(int)
        min_coords = np.min(final_positions_int, axis=0)
        final_positions_int -= min_coords
        
        if len(images[0].shape) == 3:
            h, w, c = images[0].shape
        else:
            h, w = images[0].shape
            c = 1
            images = [img.reshape(h, w, 1) if len(img.shape) == 2 else img for img in images]
        
        canvas_shape = np.max(final_positions_int, axis=0) + np.array([h, w])
        required_memory = MemoryManager.estimate_memory_usage(canvas_shape, c)
        
        self.logger.info(f"CPU融合画布大小: {canvas_shape[0]} x {canvas_shape[1]} x {c}")
        
        # 检查内存并处理分块
        if not MemoryManager.check_memory_availability(required_memory, self.config.memory_limit_gb, self.logger):
            self.logger.warning("尝试分块处理...")
            return self._blend_cpu_chunked(images, final_positions_int, canvas_shape, c)
        
        try:
            if c == 1:
                canvas = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
                weight_sum = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
            else:
                canvas = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.float32)
                weight_sum = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.float32)
            
            self.logger.info(f"成功分配CPU内存: {required_memory:.2f} GB")
            
            for i in tqdm(range(len(images)), desc="CPU融合处理"):
                img = images[i]
                img_y, img_x = final_positions_int[i]
                img_h, img_w = img.shape[:2]
                
                y_end = min(img_y + img_h, canvas_shape[0])
                x_end = min(img_x + img_w, canvas_shape[1])
                actual_h = y_end - img_y
                actual_w = x_end - img_x
                
                if actual_h <= 0 or actual_w <= 0:
                    continue
                
                img_roi = img[:actual_h, :actual_w].astype(np.float32)
                if len(img_roi.shape) == 2 and c > 1:
                    img_roi = img_roi.reshape(actual_h, actual_w, 1)
                
                # 使用线性融合权重图
                weight_map = self.create_linear_blend_weight_map(actual_h, actual_w)
                
                canvas_y1, canvas_x1 = img_y, img_x
                canvas_y2, canvas_x2 = img_y + actual_h, img_x + actual_w
                
                if c == 1:
                    if len(img_roi.shape) == 3:
                        img_roi = img_roi.squeeze(-1)
                    canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += img_roi * weight_map
                else:
                    for ch in range(min(c, img_roi.shape[2] if len(img_roi.shape) == 3 else 1)):
                        if len(img_roi.shape) == 3:
                            canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_roi[:, :, ch] * weight_map
                        else:
                            canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2, ch] += img_roi * weight_map
                
                weight_sum[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += weight_map
            
            weight_sum = np.maximum(weight_sum, 1e-10)
            
            if c == 1:
                canvas /= weight_sum
            else:
                for ch in range(c):
                    canvas[:, :, ch] /= weight_sum
            
            result = np.clip(canvas, 0, 255).astype(np.uint8)
            
            if c == 1:
                result = result.squeeze() if result.ndim > 2 else result
            
            self.logger.info("优化CPU图像融合完成")
            return result
            
        except MemoryError as e:
            self.logger.error(f"CPU内存分配失败: {e}")
            self.logger.warning("尝试分块处理...")
            return self._blend_cpu_chunked(images, final_positions_int, canvas_shape, c)
    
    def _blend_cpu_chunked(self, images: List[np.ndarray], final_positions_int: np.ndarray, 
                          canvas_shape: np.ndarray, c: int) -> Optional[np.ndarray]:
        """分块CPU融合（内存不足时的备选方案）"""
        self.logger.info("开始分块CPU融合...")
        
        chunk_size = 4096  # 4K块大小
        
        try:
            if c == 1:
                result = np.zeros((canvas_shape[0], canvas_shape[1]), dtype=np.uint8)
            else:
                result = np.zeros((canvas_shape[0], canvas_shape[1], c), dtype=np.uint8)
            
            for y_start in tqdm(range(0, canvas_shape[0], chunk_size), desc="分块处理"):
                y_end = min(y_start + chunk_size, canvas_shape[0])
                
                for x_start in range(0, canvas_shape[1], chunk_size):
                    x_end = min(x_start + chunk_size, canvas_shape[1])
                    
                    chunk_h = y_end - y_start
                    chunk_w = x_end - x_start
                    
                    if c == 1:
                        chunk_canvas = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                        chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                    else:
                        chunk_canvas = np.zeros((chunk_h, chunk_w, c), dtype=np.float32)
                        chunk_weight = np.zeros((chunk_h, chunk_w), dtype=np.float32)
                    
                    for i, img in enumerate(images):
                        img_y, img_x = final_positions_int[i]
                        img_h, img_w = img.shape[:2]
                        
                        # 检查图像是否与当前块重叠
                        if (img_x + img_w <= x_start or img_x >= x_end or 
                            img_y + img_h <= y_start or img_y >= y_end):
                            continue
                        
                        # 计算重叠区域
                        overlap_x1 = max(img_x, x_start)
                        overlap_y1 = max(img_y, y_start)
                        overlap_x2 = min(img_x + img_w, x_end)
                        overlap_y2 = min(img_y + img_h, y_end)
                        
                        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
                            continue
                        
                        # 在图像中的坐标
                        img_x1 = overlap_x1 - img_x
                        img_y1 = overlap_y1 - img_y
                        img_x2 = overlap_x2 - img_x
                        img_y2 = overlap_y2 - img_y
                        
                        # 在块中的坐标
                        chunk_x1 = overlap_x1 - x_start
                        chunk_y1 = overlap_y1 - y_start
                        chunk_x2 = overlap_x2 - x_start
                        chunk_y2 = overlap_y2 - y_start
                        
                        img_roi = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)
                        if len(img_roi.shape) == 2 and c > 1:
                            img_roi = img_roi.reshape(img_roi.shape[0], img_roi.shape[1], 1)
                        
                        weight_map = self.create_weight_map_cpu(img_h, img_w, self.config.max_feather_pixels)
                        weight_roi = weight_map[img_y1:img_y2, img_x1:img_x2]
                        
                        if c == 1:
                            if len(img_roi.shape) == 3:
                                img_roi = img_roi.squeeze(-1)
                            chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += img_roi * weight_roi
                        else:
                            for ch in range(min(c, img_roi.shape[2] if len(img_roi.shape) == 3 else 1)):
                                if len(img_roi.shape) == 3:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_roi[:, :, ch] * weight_roi
                                else:
                                    chunk_canvas[chunk_y1:chunk_y2, chunk_x1:chunk_x2, ch] += img_roi * weight_roi
                        
                        chunk_weight[chunk_y1:chunk_y2, chunk_x1:chunk_x2] += weight_roi
                    
                    # 归一化和保存块
                    chunk_weight = np.maximum(chunk_weight, 1e-10)
                    
                    if c == 1:
                        chunk_result = chunk_canvas / chunk_weight
                    else:
                        chunk_result = np.zeros_like(chunk_canvas)
                        for ch in range(c):
                            chunk_result[:, :, ch] = chunk_canvas[:, :, ch] / chunk_weight
                    
                    chunk_result = np.clip(chunk_result, 0, 255).astype(np.uint8)
                    result[y_start:y_end, x_start:x_end] = chunk_result
            
            self.logger.info("分块CPU图像融合完成")
            return result
            
        except Exception as e:
            self.logger.error(f"分块融合失败: {e}")
            return None


class ResultSaver:
    """结果保存器"""
    
    @staticmethod
    def save_config(path: str, filenames: List[str], final_positions: np.ndarray, logger: Logger) -> None:
        """保存优化后的坐标到文件"""
        logger.info(f"正在将校正后坐标写入: {path}")
        with open(path, 'w', encoding='utf-8') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates\n")
            for i, filename in enumerate(filenames):
                y, x = final_positions[i]
                f.write(f"{filename}; ; ({x:.1f}, {y:.1f})\n")
        logger.info("坐标文件写入成功。")
    
    @staticmethod
    def save_result(stitched_image: Optional[np.ndarray], output_filename: str, 
                   config: StitchingConfig, start_time: float, num_images: int, logger: Logger) -> None:
        """保存最终结果并统计性能"""
        if stitched_image is not None:
            if config.save_format.lower() in ['jpg', 'jpeg']:
                cv2.imwrite(output_filename, stitched_image, [cv2.IMWRITE_JPEG_QUALITY, config.jpeg_quality])
                logger.info(f"拼接图像已保存至: {output_filename} (JPEG质量: {config.jpeg_quality}%)")
            elif config.save_format.lower() == 'png':
                compression_level = 6
                cv2.imwrite(output_filename, stitched_image, [cv2.IMWRITE_PNG_COMPRESSION, compression_level])
                logger.info(f"拼接图像已保存至: {output_filename} (PNG压缩级别: {compression_level})")
            elif config.save_format.lower() in ['tif', 'tiff']:
                cv2.imwrite(output_filename, stitched_image)
                logger.info(f"拼接图像已保存至: {output_filename} (TIFF无损格式)")
            else:
                cv2.imwrite(output_filename, stitched_image)
                logger.info(f"拼接图像已保存至: {output_filename}")
            
            try:
                file_size_bytes = os.path.getsize(output_filename)
                file_size_mb = file_size_bytes / (1024 * 1024)
                logger.info(f"文件大小: {file_size_mb:.1f} MB ({file_size_bytes:,} 字节)")
                
                if len(stitched_image.shape) == 3:
                    h, w, c = stitched_image.shape
                    total_pixels = h * w
                    logger.info(f"图像尺寸: {w} x {h} x {c} ({total_pixels:,} 像素)")
                else:
                    h, w = stitched_image.shape
                    total_pixels = h * w
                    logger.info(f"图像尺寸: {w} x {h} ({total_pixels:,} 像素)")
                    
                uncompressed_size_mb = (total_pixels * 3) / (1024 * 1024)
                compression_ratio = uncompressed_size_mb / file_size_mb if file_size_mb > 0 else 1
                logger.info(f"压缩比: {compression_ratio:.1f}:1 (未压缩: {uncompressed_size_mb:.1f} MB)")
                
            except Exception as e:
                logger.warning(f"无法获取文件大小信息: {e}")
        else:
            logger.info("由于内存限制，跳过了图像融合")
        
        total_time = time.time() - start_time
        logger.info(f"=== 任务完成！总耗时: {total_time:.2f} 秒 ===")
    
    def stitch_images(self, image_dir: str, config_filename: str = "TileConfiguration.txt",
                     output_filename: Optional[str] = None, 
                     registered_filename: Optional[str] = None,
                     log_filename: Optional[str] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
        """
        执行图像拼接 - 主要接口
        
        Args:
            image_dir: 图像目录路径
            config_filename: 配置文件名
            output_filename: 输出图像文件名（可选）
            registered_filename: 注册坐标文件名（可选）
            log_filename: 日志文件名（可选）
            
        Returns:
            Tuple[拼接后的图像, 最终位置坐标]
        """
        # 设置默认文件名
        if output_filename is None:
            output_filename = os.path.join(image_dir, "stitched_result.jpg")
        if registered_filename is None:
            registered_filename = os.path.join(image_dir, "TileConfiguration.registered_python.txt")
        if log_filename is None:
            log_filename = os.path.join(image_dir, "stitching_log.txt")
        
        # 初始化日志
        self._setup_logging(log_filename)
        start_time = time.time()
        
        if self.logger:
            self.logger.info("=== 超高速图像拼接开始 ===")
            self.logger.info(f"使用线程数: {self.config.max_workers}")
            self.logger.info(f"特征点数量: {self.config.feature_count}")
        
        try:
            # 解析配置文件
            config_path = os.path.join(image_dir, config_filename)
            filenames, initial_positions = ConfigurationParser.parse_tile_config(config_path, self.logger)

            # 分析图像布局
            idx_001, idx_002, idx_032 = ImageLayoutAnalyzer.analyze_layout(filenames, initial_positions, self.logger)
            
            # 加载图像
            gray_images = ImageLoader.load_images(image_dir, filenames, False, self.logger)
            
            # 寻找邻居对
            all_neighbors = ImageLayoutAnalyzer.find_grid_neighbors(initial_positions, self.logger)
            
            # 特征匹配
            pairwise_shifts = self.find_pairwise_shifts(gray_images, all_neighbors, filenames, initial_positions)
            
            # 全局优化
            final_positions = self.optimize_positions(len(filenames), initial_positions, pairwise_shifts, filenames)
            
            # 保存注册结果
            ResultSaver.save_config(registered_filename, filenames, final_positions, self.logger)
            
            # 图像融合
            color_images = ImageLoader.load_images(image_dir, filenames, True, self.logger)
            blending_engine = BlendingEngine(self.config, self.logger)
            stitched_image = blending_engine.blend_gpu(color_images, final_positions)
            
            # 保存结果
            ResultSaver.save_result(stitched_image, output_filename, self.config, start_time, len(filenames), self.logger)

            return stitched_image, final_positions
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"拼接过程中发生错误: {e}")
            raise


# ===== 便捷工厂函数 =====

def create_stitcher(feature_count: int = 800, max_workers: int = 20, 
                   memory_limit_gb: float = 45.0, use_gpu: bool = True) -> ImageStitcher:
    """
    创建图像拼接器的便捷工厂函数
    
    Args:
        feature_count: 特征点数量
        max_workers: 最大工作线程数
        memory_limit_gb: 内存限制（GB）
        use_gpu: 是否使用GPU加速
        
    Returns:
        配置好的ImageStitcher实例
    """
    config = StitchingConfig(
        feature_count=feature_count,
        max_workers=max_workers,
        memory_limit_gb=memory_limit_gb,
        use_gpu=use_gpu
    )
    return ImageStitcher(config)


def stitch_directory(image_dir: str, output_filename: Optional[str] = None, 
                    config: Optional[StitchingConfig] = None) -> Tuple[Optional[np.ndarray], np.ndarray]:
    """
    拼接目录中的图像的便捷函数
    
    Args:
        image_dir: 图像目录路径
        output_filename: 输出文件名（可选）
        config: 拼接配置（可选）
        
    Returns:
        Tuple[拼接后的图像, 最终位置坐标]
    """
    stitcher = ImageStitcher(config)
    return stitcher.stitch_images(image_dir, output_filename=output_filename)


# ======================== 程序入口点 ========================

if __name__ == '__main__':
    # 配置区域
    IMAGE_DIRECTORY = r"Image_SX88"
    CONFIG_FILE = "TileConfiguration.txt"
    REGISTERED_CONFIG_FILE = "TileConfiguration.registered_python.txt"
    LOG_FILE = "Log2.txt"
    OUTPUT_IMAGE_FILE = "Stitched_Image_SX88.jpg"
    
    # 创建拼接配置
    stitching_config = StitchingConfig(
        feature_count=1000,
        max_workers=12,
        overlap_ratio=0.1,
        min_confidence=0.1,
        max_iterations=20,
        abs_threshold=10.0,
        ratio_threshold=2.0,
        memory_limit_gb=32,  # 增加内存限制
        gpu_memory_limit_gb=3.5,  # 优化GPU内存限制，充分利用16GB显存
        gpu_batch_size=1,  # 减少批处理大小
        gpu_chunk_size=4096,  # GPU分块大小
        jpeg_quality=85,
        save_format="jpg"
    )
    
    # 验证输入
    if not os.path.isdir(IMAGE_DIRECTORY):
        print(f"错误: 找不到图像文件夹 '{IMAGE_DIRECTORY}'")
        sys.exit(1)
    
    # 准备路径
    multiprocessing.freeze_support()
    
    # 执行拼接
    try:
        # 方式1：使用类接口
        stitcher = ImageStitcher(stitching_config)
        stitched_image, final_positions = stitcher.stitch_images(
            IMAGE_DIRECTORY, CONFIG_FILE, OUTPUT_IMAGE_FILE, 
            REGISTERED_CONFIG_FILE, LOG_FILE
        )
        
        # 方式2：使用便捷函数（注释掉的替代方案）
        # stitched_image, final_positions = stitch_directory(IMAGE_DIRECTORY, OUTPUT_IMAGE_FILE, stitching_config)
        
    except Exception as e:
        print(f"拼接过程中发生错误: {e}")
        sys.exit(1)