#!/usr/bin/env python3
"""
测试相位相关图像拼接算法
基于ImageJ Grid/Collection Stitching的Python实现

使用方法:
python test_phase_correlation_stitching.py
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入拼接模块
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
    stitching_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(stitching_module)
    ImageStitcher = stitching_module.ImageStitcher
    StitchingConfig = stitching_module.StitchingConfig
    print("成功导入拼接模块")
except Exception as e:
    print(f"导入拼接模块失败: {e}")
    sys.exit(1)


def test_phase_correlation_stitching():
    """测试相位相关拼接算法"""
    print("=" * 60)
    print("ImageJ Grid/Collection Stitching 相位相关算法测试")
    print("=" * 60)
    
    # 配置参数
    config = StitchingConfig(
        # 特征匹配参数
        feature_count=1000,
        overlap_ratio=0.10,  # 基于分析的10%重叠度
        min_confidence=0.15,
        fallback_confidence=0.08,
        
        # 全局优化参数 - 2-3轮优化
        max_iterations=25,
        abs_threshold=8.0,
        ratio_threshold=2.5,
        max_position_change=25.0,
        constraint_weight=0.015,
        
        # 融合参数 - 线性融合
        feather_pixels_ratio=0.08,
        max_feather_pixels=40,
        min_feather_pixels=8,
        memory_limit_gb=48.0,
        
        # GPU加速参数
        use_gpu=True,
        gpu_memory_limit_gb=15.0,
        gpu_batch_size=2,
        gpu_chunk_size=6144,
        
        # 图像保存参数
        jpeg_quality=90,
        save_format="jpg",
        
        # 并行处理参数
        max_workers=16
    )
    
    # 创建拼接器
    stitcher = ImageStitcher(config)
    
    # 设置路径
    image_dir = "image_55"
    config_file = "TileConfiguration.txt"
    output_file = "stitched_phase_correlation_result.jpg"
    registered_file = "TileConfiguration.registered_phase_correlation.txt"
    log_file = "phase_correlation_stitching_log.txt"
    
    # 检查输入目录
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录 '{image_dir}' 不存在")
        return False
    
    config_path = os.path.join(image_dir, config_file)
    if not os.path.exists(config_path):
        print(f"错误: 配置文件 '{config_path}' 不存在")
        return False
    
    print(f"输入目录: {image_dir}")
    print(f"配置文件: {config_file}")
    print(f"输出文件: {output_file}")
    print(f"注册文件: {registered_file}")
    print(f"日志文件: {log_file}")
    print()
    
    # 执行拼接
    try:
        start_time = time.time()
        
        print("开始相位相关图像拼接...")
        stitched_image, final_positions = stitcher.stitch_images(
            image_dir=image_dir,
            config_filename=config_file,
            output_filename=output_file,
            registered_filename=registered_file,
            log_filename=log_file
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        if stitched_image is not None:
            print(f"\n✅ 拼接成功完成!")
            print(f"总耗时: {total_time:.2f} 秒")
            print(f"输出图像尺寸: {stitched_image.shape}")
            print(f"输出文件: {output_file}")
            print(f"注册坐标文件: {registered_file}")
            print(f"日志文件: {log_file}")
            
            # 显示最终位置统计
            if final_positions is not None:
                print(f"\n最终位置统计:")
                print(f"  图像数量: {len(final_positions)}")
                print(f"  位置范围 Y: {final_positions[:, 0].min():.1f} ~ {final_positions[:, 0].max():.1f}")
                print(f"  位置范围 X: {final_positions[:, 1].min():.1f} ~ {final_positions[:, 1].max():.1f}")
            
            return True
        else:
            print("❌ 拼接失败")
            return False
            
    except Exception as e:
        print(f"❌ 拼接过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("ImageJ Grid/Collection Stitching 相位相关算法测试")
    print("基于TileConfiguration.txt的96张图像拼接")
    print()
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("警告: 建议使用Python 3.7或更高版本")
    
    # 运行测试
    success = test_phase_correlation_stitching()
    
    if success:
        print("\n🎉 测试完成! 相位相关拼接算法运行成功")
        print("\n算法特点:")
        print("✓ 基于ImageJ PhaseCorrelation的FFT相位相关算法")
        print("✓ 亚像素精度配准和多峰值检测")
        print("✓ 精确的重叠区域ROI计算")
        print("✓ 2-3轮全局迭代优化")
        print("✓ 线性融合算法，支持羽化边缘")
        print("✓ GPU加速融合（如果可用）")
    else:
        print("\n❌ 测试失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
