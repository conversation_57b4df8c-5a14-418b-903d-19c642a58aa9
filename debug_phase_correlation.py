#!/usr/bin/env python3
"""
调试相位相关算法
检查算法实现中的问题
"""

import os
import sys
import numpy as np
import cv2
import importlib.util
from scipy.fft import fft2, ifft2, fftshift
import matplotlib.pyplot as plt

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        return None

def simple_phase_correlation(img1, img2):
    """简单的相位相关实现用于对比"""
    # 转换为float
    f1 = img1.astype(np.float64)
    f2 = img2.astype(np.float64)
    
    # FFT
    F1 = fft2(f1)
    F2 = fft2(f2)
    
    # 互功率谱
    cross_power = F1 * np.conj(F2)
    magnitude = np.abs(cross_power)
    magnitude[magnitude == 0] = 1e-15
    
    # 归一化
    normalized = cross_power / magnitude
    
    # 逆FFT
    correlation = np.real(ifft2(normalized))
    correlation = fftshift(correlation)
    
    # 找峰值
    peak_idx = np.unravel_index(np.argmax(correlation), correlation.shape)
    center_y, center_x = correlation.shape[0] // 2, correlation.shape[1] // 2
    
    shift_y = peak_idx[0] - center_y
    shift_x = peak_idx[1] - center_x
    
    return np.array([shift_y, shift_x]), correlation[peak_idx]

def debug_phase_correlation():
    """调试相位相关算法"""
    print("=" * 60)
    print("相位相关算法调试")
    print("=" * 60)
    
    # 加载模块
    stitching = load_stitching_module()
    if stitching is None:
        return False
    
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    # 创建拼接器
    config = StitchingConfig()
    stitcher = ImageStitcher(config)
    
    # 创建简单测试图像
    img1 = np.zeros((128, 128), dtype=np.uint8)
    cv2.circle(img1, (64, 64), 20, 255, -1)
    cv2.rectangle(img1, (50, 50), (78, 78), 128, 2)
    
    # 测试已知位移
    test_shift = (2, 3)  # 整像素位移
    M = np.float32([[1, 0, test_shift[1]], [0, 1, test_shift[0]]])
    img2 = cv2.warpAffine(img1, M, (128, 128))
    
    print(f"真实位移: {test_shift}")
    print()
    
    # 1. 测试简单实现
    print("1. 简单相位相关实现:")
    try:
        simple_shift, simple_corr = simple_phase_correlation(img1, img2)
        print(f"   检测位移: ({simple_shift[0]:.3f}, {simple_shift[1]:.3f})")
        print(f"   相关系数: {simple_corr:.6f}")
        simple_error = np.linalg.norm(simple_shift - np.array(test_shift))
        print(f"   误差: {simple_error:.3f}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    print()
    
    # 2. 测试完整实现
    print("2. 完整相位相关实现:")
    try:
        pos1 = np.array([0.0, 0.0])
        pos2 = np.array([0.0, 0.0])
        
        detected_shift, correlation = stitcher.phase_correlation_registration(
            img1, img2, pos1, pos2, num_peaks=3, subpixel_accuracy=False
        )
        
        print(f"   检测位移: ({detected_shift[0]:.3f}, {detected_shift[1]:.3f})")
        print(f"   相关系数: {correlation:.6f}")
        full_error = np.linalg.norm(detected_shift - np.array(test_shift))
        print(f"   误差: {full_error:.3f}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 3. 测试亚像素实现
    print("3. 亚像素相位相关实现:")
    try:
        detected_shift_sub, correlation_sub = stitcher.phase_correlation_registration(
            img1, img2, pos1, pos2, num_peaks=3, subpixel_accuracy=True
        )
        
        print(f"   检测位移: ({detected_shift_sub[0]:.3f}, {detected_shift_sub[1]:.3f})")
        print(f"   相关系数: {correlation_sub:.6f}")
        sub_error = np.linalg.norm(detected_shift_sub - np.array(test_shift))
        print(f"   误差: {sub_error:.3f}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 4. 测试ROI提取
    print("4. ROI提取测试:")
    try:
        roi1, roi2, offset1, offset2 = stitcher.get_overlap_roi(img1, img2, pos1, pos2)
        print(f"   ROI1 尺寸: {roi1.shape}")
        print(f"   ROI2 尺寸: {roi2.shape}")
        print(f"   偏移1: {offset1}")
        print(f"   偏移2: {offset2}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    return True

def main():
    """主函数"""
    print("ImageJ 相位相关算法调试工具")
    print("用于检查和修复算法实现问题")
    print()
    
    debug_phase_correlation()
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
