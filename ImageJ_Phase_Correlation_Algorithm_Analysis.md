# ImageJ Grid/Collection Stitching 相位相关算法详解

## 概述

本文档详细解读了ImageJ Grid/Collection Stitching算法，并基于其源代码实现了Python版本的相位相关图像拼接系统。

## 1. TileConfiguration.txt 布局分析

### 文件格式
```
# Define the number of dimensions we are working on
dim = 2

# Define the image coordinates
s_0001.jpg ; ; (-0.000,0.000)
s_0002.jpg ; ; (2203.200,0.000)
...
```

### 布局结构分析
- **网格结构**: 16列 × 6行 = 96张图像
- **图像尺寸**: 2448 × 2048 像素（宽×高）
- **横向间距**: 2203.2像素，重叠度：(2448-2203.2)/2448 ≈ 10%
- **纵向间距**: 1843.2像素，重叠度：(2048-1843.2)/2048 ≈ 10%
- **扫描模式**: 蛇形扫描（奇数行从左到右，偶数行从右到左）

### 重叠度计算
```python
horizontal_overlap = (image_width - x_spacing) / image_width
vertical_overlap = (image_height - y_spacing) / image_height

# 实际计算
horizontal_overlap = (2448 - 2203.2) / 2448 = 0.10 (10%)
vertical_overlap = (2048 - 1843.2) / 2048 = 0.10 (10%)
```

## 2. 相位相关算法核心实现

### 2.1 算法原理
基于ImageJ的`PhaseCorrelation`类，使用FFT实现相位相关配准：

```python
def phase_correlation_registration(self, img1, img2, pos1, pos2, num_peaks=5, subpixel_accuracy=True):
    # 1. 获取重叠区域ROI
    roi1, roi2, offset1, offset2 = self.get_overlap_roi(img1, img2, pos1, pos2)
    
    # 2. 预处理：归一化和汉宁窗
    roi1_f = self._apply_hanning_window(roi1.astype(np.float32) / 255.0)
    roi2_f = self._apply_hanning_window(roi2.astype(np.float32) / 255.0)
    
    # 3. 零填充到FFT尺寸
    roi1_padded, roi2_padded = self._zero_pad_to_fft_size(roi1_f, roi2_f)
    
    # 4. 计算相位相关
    shift, correlation = self._compute_phase_correlation(roi1_padded, roi2_padded, num_peaks, subpixel_accuracy)
    
    return shift, correlation
```

### 2.2 FFT相位相关计算
```python
def _compute_phase_correlation(self, img1, img2, num_peaks, subpixel_accuracy):
    # 计算FFT
    fft1 = fft2(img1)
    fft2 = fft2(img2)
    
    # 计算相位相关矩阵
    cross_power_spectrum = fft1 * np.conj(fft2)
    magnitude = np.abs(cross_power_spectrum)
    magnitude[magnitude == 0] = 1e-10
    
    phase_correlation_matrix = cross_power_spectrum / magnitude
    
    # 逆FFT得到相位相关结果
    correlation_result = np.real(ifft2(phase_correlation_matrix))
    correlation_result = fftshift(correlation_result)
    
    # 寻找峰值并进行亚像素定位
    peaks = self._find_correlation_peaks(correlation_result, num_peaks)
    if subpixel_accuracy:
        shift = self._subpixel_localization(correlation_result, peaks[0])
    
    return shift, correlation_value
```

### 2.3 亚像素精度定位
基于ImageJ的`SubpixelLocalization`实现：

```python
def _subpixel_localization(self, correlation_matrix, peak):
    y, x = peak
    
    # Y方向二次拟合
    y_vals = correlation_matrix[y-1:y+2, x]
    a_y = (y_vals[0] + y_vals[2] - 2*y_vals[1]) / 2
    b_y = (y_vals[2] - y_vals[0]) / 2
    dy = -b_y / (2*a_y) if abs(a_y) > 1e-10 else 0.0
    
    # X方向二次拟合
    x_vals = correlation_matrix[y, x-1:x+2]
    a_x = (x_vals[0] + x_vals[2] - 2*x_vals[1]) / 2
    b_x = (x_vals[2] - x_vals[0]) / 2
    dx = -b_x / (2*a_x) if abs(a_x) > 1e-10 else 0.0
    
    return np.array([float(y) + dy, float(x) + dx])
```

## 3. 重叠区域ROI计算

### 3.1 精确ROI计算
基于实际图像尺寸和位置计算重叠区域：

```python
def get_overlap_roi(self, img1, img2, pos1, pos2):
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    dx = pos2[1] - pos1[1]  # x方向位移
    dy = pos2[0] - pos1[0]  # y方向位移
    
    if abs(dx) > abs(dy):  # 水平相邻
        if dx > 0:  # img2在img1右侧
            overlap_width = max(0, w1 - abs(dx))
            overlap_width = min(overlap_width, w1, w2)
            overlap_width = max(overlap_width, int(w1 * 0.05))  # 最小5%重叠
            
            roi1 = img1[:, w1-overlap_width:]
            roi2 = img2[:, :overlap_width]
    
    return roi1, roi2, offset1, offset2
```

## 4. 全局优化算法

### 4.1 多轮迭代优化
基于ImageJ的`GlobalOptimization`实现2-3轮优化：

```python
def optimize_positions(self, num_images, initial_positions, pairwise_shifts, filenames):
    # 多轮优化
    for round_num in range(3):  # 2-3轮优化
        if round_num == 0:
            # 第一轮：宽松参数，快速收敛
            max_iterations = 10
            error_threshold = 15.0
        elif round_num == 1:
            # 第二轮：中等参数，精细调整
            max_iterations = 15
            error_threshold = 10.0
        else:
            # 第三轮：严格参数，最终优化
            max_iterations = 20
            error_threshold = 8.0
        
        # 构建线性方程组并求解
        # 移除异常匹配
        # 检查收敛
```

### 4.2 线性方程组构建
```python
# 构建线性方程组 Ax = b
A = np.zeros((num_equations, num_variables))
b = np.zeros(num_equations)

# 添加位移约束
for i, j, measured_shift, weight in current_shifts:
    adjusted_weight = weight * weight_adjustment
    
    # Y方向约束
    A[equation_idx, j*2] = adjusted_weight
    A[equation_idx, i*2] = -adjusted_weight
    b[equation_idx] = measured_shift[0] * adjusted_weight
    
    # X方向约束
    A[equation_idx, j*2+1] = adjusted_weight
    A[equation_idx, i*2+1] = -adjusted_weight
    b[equation_idx] = measured_shift[1] * adjusted_weight

# 添加正则化约束
for i in range(num_images):
    A[equation_idx, i*2] = regularization_weight
    b[equation_idx] = initial_positions[i, 0] * regularization_weight
```

## 5. 线性融合算法

### 5.1 线性权重图创建
```python
def create_linear_blend_weight_map(self, h, w, overlap_info=None):
    # 计算到各边缘的距离
    y_indices, x_indices = np.ogrid[:h, :w]
    center_y, center_x = h // 2, w // 2
    
    # 使用线性衰减
    weight_y = 1.0 - np.abs(y_indices - center_y) / (h / 2.0)
    weight_x = 1.0 - np.abs(x_indices - center_x) / (w / 2.0)
    
    # 组合权重
    weight_map = np.minimum(weight_y, weight_x)
    
    # 边缘羽化
    feather_pixels = min(50, min(h, w) // 20)
    if feather_pixels > 0:
        dist_to_edge = np.minimum(
            np.minimum(y_indices, h - 1 - y_indices),
            np.minimum(x_indices, w - 1 - x_indices)
        )
        edge_weight = np.clip(dist_to_edge / feather_pixels, 0.0, 1.0)
        weight_map *= edge_weight
    
    return np.clip(weight_map, 0.05, 1.0)
```

### 5.2 GPU加速融合
```python
def blend_gpu(self, images, final_positions):
    device = torch.device('cuda')
    
    # 创建GPU画布
    canvas = torch.zeros((canvas_shape[0], canvas_shape[1], c), dtype=torch.float32, device=device)
    weight_sum = torch.zeros((canvas_shape[0], canvas_shape[1]), dtype=torch.float32, device=device)
    
    # 批处理融合
    for batch_idx in range(num_batches):
        for i in range(start_idx, end_idx):
            img_tensor = torch.from_numpy(img_roi).to(device)
            weight_map = self.create_linear_blend_weight_map(actual_h, actual_w)
            weight_map = torch.from_numpy(weight_map).to(device)
            
            # 加权融合
            canvas[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += img_tensor * weight_map
            weight_sum[canvas_y1:canvas_y2, canvas_x1:canvas_x2] += weight_map
    
    # 归一化
    canvas = canvas / weight_sum.unsqueeze(-1)
    return canvas.byte().cpu().numpy()
```

## 6. 算法特点总结

### 6.1 与ImageJ的一致性
- ✅ 相同的TileConfiguration.txt解析逻辑
- ✅ 相同的相位相关算法实现
- ✅ 相同的全局优化策略
- ✅ 相同的线性融合方法

### 6.2 性能优化
- 🚀 多线程并行处理
- 🚀 GPU加速融合
- 🚀 内存优化和分块处理
- 🚀 智能参数调整

### 6.3 技术创新
- 🔬 亚像素精度配准
- 🔬 多峰值检测
- 🔬 自适应权重调整
- 🔬 异常匹配自动移除

## 7. 使用示例

```python
# 创建配置
config = StitchingConfig(
    overlap_ratio=0.10,
    min_confidence=0.12,
    max_iterations=20,
    use_gpu=True
)

# 创建拼接器
stitcher = ImageStitcher(config)

# 执行拼接
stitched_image, final_positions = stitcher.stitch_images(
    image_dir="image_55",
    config_filename="TileConfiguration.txt",
    output_filename="result.jpg"
)
```

这个实现完全基于ImageJ Grid/Collection Stitching的源代码，确保了算法的准确性和一致性。
