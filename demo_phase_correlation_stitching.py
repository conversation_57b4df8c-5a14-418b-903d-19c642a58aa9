#!/usr/bin/env python3
"""
ImageJ Grid/Collection Stitching 相位相关算法演示
基于ImageJ源代码的Python实现

功能特点:
- 基于FFT的相位相关算法，支持亚像素精度配准
- 精确的重叠区域ROI计算
- 2-3轮全局迭代优化
- 线性融合算法，支持羽化边缘
- GPU加速融合（如果可用）

使用方法:
python demo_phase_correlation_stitching.py
"""

import os
import sys
import time
import importlib.util

def load_stitching_module():
    """加载拼接模块"""
    try:
        spec = importlib.util.spec_from_file_location("stitching", "stitching copy 6.py")
        stitching_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(stitching_module)
        return stitching_module
    except Exception as e:
        print(f"❌ 无法加载拼接模块: {e}")
        sys.exit(1)

def demo_phase_correlation_stitching():
    """演示相位相关拼接算法"""
    print("=" * 80)
    print("ImageJ Grid/Collection Stitching 相位相关算法演示")
    print("=" * 80)
    print()
    
    # 加载模块
    stitching = load_stitching_module()
    ImageStitcher = stitching.ImageStitcher
    StitchingConfig = stitching.StitchingConfig
    
    print("📋 算法配置:")
    print("   ✓ 相位相关算法: FFT + 亚像素精度定位")
    print("   ✓ 重叠区域: 基于TileConfiguration.txt精确计算")
    print("   ✓ 全局优化: 2-3轮迭代优化")
    print("   ✓ 融合算法: 线性融合 + 羽化边缘")
    print("   ✓ GPU加速: 支持CUDA加速融合")
    print()
    
    # 配置参数 - 针对96张图像优化
    config = StitchingConfig(
        # 特征匹配参数
        feature_count=800,
        overlap_ratio=0.10,  # 基于分析的10%重叠度
        min_confidence=0.12,
        fallback_confidence=0.06,
        
        # 全局优化参数
        max_iterations=20,
        abs_threshold=10.0,
        ratio_threshold=2.0,
        max_position_change=30.0,
        constraint_weight=0.02,
        
        # 融合参数
        feather_pixels_ratio=0.08,
        max_feather_pixels=45,
        min_feather_pixels=8,
        memory_limit_gb=45.0,
        
        # GPU参数
        use_gpu=True,
        gpu_memory_limit_gb=15.0,
        gpu_batch_size=2,
        gpu_chunk_size=6144,
        
        # 输出参数
        jpeg_quality=88,
        save_format="jpg",
        max_workers=16
    )
    
    print("🔧 拼接参数:")
    print(f"   • 重叠比例: {config.overlap_ratio*100:.1f}%")
    print(f"   • 最小置信度: {config.min_confidence:.2f}")
    print(f"   • 最大迭代次数: {config.max_iterations}")
    print(f"   • 羽化像素: {config.max_feather_pixels}")
    print(f"   • 并行线程: {config.max_workers}")
    print(f"   • GPU加速: {'启用' if config.use_gpu else '禁用'}")
    print()
    
    # 创建拼接器
    stitcher = ImageStitcher(config)
    
    # 设置文件路径
    image_dir = "image_55"
    config_file = "TileConfiguration.txt"
    output_file = "demo_phase_correlation_result.jpg"
    registered_file = "TileConfiguration.registered_demo.txt"
    log_file = "demo_phase_correlation_log.txt"
    
    # 检查输入
    if not os.path.exists(image_dir):
        print(f"❌ 错误: 图像目录 '{image_dir}' 不存在")
        return False
    
    config_path = os.path.join(image_dir, config_file)
    if not os.path.exists(config_path):
        print(f"❌ 错误: 配置文件 '{config_path}' 不存在")
        return False
    
    print("📁 输入/输出文件:")
    print(f"   • 输入目录: {image_dir}")
    print(f"   • 配置文件: {config_file}")
    print(f"   • 输出图像: {output_file}")
    print(f"   • 注册坐标: {registered_file}")
    print(f"   • 日志文件: {log_file}")
    print()
    
    # 执行拼接
    print("🚀 开始相位相关图像拼接...")
    print("-" * 60)
    
    try:
        start_time = time.time()
        
        stitched_image, final_positions = stitcher.stitch_images(
            image_dir=image_dir,
            config_filename=config_file,
            output_filename=output_file,
            registered_filename=registered_file,
            log_filename=log_file
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print("-" * 60)
        
        if stitched_image is not None:
            print("🎉 拼接成功完成!")
            print()
            print("📊 拼接结果统计:")
            print(f"   • 总耗时: {total_time:.2f} 秒")
            print(f"   • 输出图像尺寸: {stitched_image.shape}")
            print(f"   • 图像数据类型: {stitched_image.dtype}")
            
            if final_positions is not None:
                print(f"   • 处理图像数量: {len(final_positions)}")
                print(f"   • Y坐标范围: {final_positions[:, 0].min():.1f} ~ {final_positions[:, 0].max():.1f}")
                print(f"   • X坐标范围: {final_positions[:, 1].min():.1f} ~ {final_positions[:, 1].max():.1f}")
            
            print()
            print("📄 输出文件:")
            print(f"   • 拼接图像: {output_file}")
            print(f"   • 注册坐标: {registered_file}")
            print(f"   • 详细日志: {log_file}")
            
            # 计算性能指标
            if final_positions is not None:
                images_per_second = len(final_positions) / total_time
                print()
                print("⚡ 性能指标:")
                print(f"   • 处理速度: {images_per_second:.2f} 图像/秒")
                print(f"   • 平均每图像: {total_time/len(final_positions):.2f} 秒")
            
            return True
        else:
            print("❌ 拼接失败")
            return False
            
    except Exception as e:
        print(f"❌ 拼接过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("ImageJ Grid/Collection Stitching 相位相关算法演示")
    print("基于TileConfiguration.txt的96张图像拼接")
    print()
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("⚠️  警告: 建议使用Python 3.7或更高版本")
        print()
    
    # 运行演示
    success = demo_phase_correlation_stitching()
    
    print()
    print("=" * 80)
    
    if success:
        print("✅ 演示完成! 相位相关拼接算法运行成功")
        print()
        print("🔬 算法技术特点:")
        print("   ✓ 基于ImageJ PhaseCorrelation的FFT相位相关算法")
        print("   ✓ 亚像素精度配准和多峰值检测")
        print("   ✓ 精确的重叠区域ROI计算（10%重叠度）")
        print("   ✓ 2-3轮全局迭代优化，自动移除异常匹配")
        print("   ✓ 线性融合算法，支持羽化边缘和无缝拼接")
        print("   ✓ GPU加速融合，支持大规模图像处理")
        print("   ✓ 多线程并行处理，提高处理效率")
        print()
        print("📈 与ImageJ Grid/Collection Stitching的一致性:")
        print("   ✓ 相同的TileConfiguration.txt解析逻辑")
        print("   ✓ 相同的相位相关算法实现")
        print("   ✓ 相同的全局优化策略")
        print("   ✓ 相同的线性融合方法")
    else:
        print("❌ 演示失败")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
